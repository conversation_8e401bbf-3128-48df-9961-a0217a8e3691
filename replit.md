# BrandForge - AI-Powered Brand Name Generator

## Overview

BrandForge is a full-stack web application that generates unique, memorable brand names using AI technology. Similar to Namlix.com, it provides users with catchy, brandable names for startups and products, complete with domain availability checking and logo generation capabilities.

The application follows a modern full-stack architecture with a React frontend, Express.js backend, and PostgreSQL database, designed to provide a seamless brand naming experience.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query for server state, React hooks for local state
- **Routing**: Wouter for lightweight client-side routing
- **Build Tool**: Vite for fast development and optimized builds

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **API Design**: RESTful endpoints with JSON responses
- **Middleware**: Express middleware for logging, error handling, and request parsing

### Data Layer
- **Database**: PostgreSQL (configured via Drizzle)
- **ORM**: Drizzle ORM for type-safe database operations
- **Schema**: Shared TypeScript schemas using Zod for validation
- **Storage**: Dual implementation with in-memory storage and database persistence

## Key Components

### 1. Brand Name Generation System
- **AI Integration**: OpenAI GPT-4 API for generating creative brand names
- **Generation Logic**: Creates 18 unique names per request with various styles (invented, blended, tech-style)
- **Name Attributes**: Each name includes description, estimated domain availability, and letter count

### 2. User Interface Components
- **Home Page**: Main interface for keyword input and name generation
- **Brand Name Cards**: Individual cards displaying generated names with action buttons
- **Favorites System**: Local storage-based system for saving preferred names
- **Modal System**: Saved names management with export capabilities

### 3. External Service Integration
- **Domain Checking**: Direct links to Namecheap for domain availability
- **Logo Generation**: Integration with Brandmark.io for instant logo creation
- **Export Functionality**: CSV export for saved brand names

### 4. Data Management
- **Local Storage**: Client-side persistence for user favorites
- **Database Storage**: Server-side storage for generated names and keywords
- **Type Safety**: Comprehensive TypeScript types and Zod validation schemas

## Data Flow

1. **User Input**: User enters a keyword/niche on the homepage
2. **API Request**: Frontend sends POST request to `/api/generate-names`
3. **AI Processing**: Backend calls OpenAI API to generate brand names
4. **Data Persistence**: Generated names are saved to database with metadata
5. **Response Delivery**: Names are returned to frontend and displayed in cards
6. **User Interaction**: Users can save favorites locally or access external services
7. **Data Export**: Users can export saved names or view historical generations

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL database connectivity
- **drizzle-orm**: Type-safe ORM for database operations
- **@tanstack/react-query**: Server state management
- **@radix-ui/***: Accessible UI primitives
- **openai**: Official OpenAI API client

### Development Tools
- **Vite**: Frontend build tool with React plugin
- **TypeScript**: Type safety across the application
- **Tailwind CSS**: Utility-first CSS framework
- **ESBuild**: Backend bundling for production

### External Services
- **OpenAI GPT-4**: AI-powered brand name generation
- **Namecheap**: Domain availability checking and registration
- **Brandmark.io**: Logo generation service

## Deployment Strategy

### Development Environment
- **Runtime**: Node.js 20 with Replit modules
- **Database**: PostgreSQL 16 instance
- **Development Server**: Concurrent frontend (Vite) and backend (tsx) processes
- **Port Configuration**: Backend on port 5000, frontend proxied through Vite

### Production Build
- **Frontend**: Vite builds optimized static assets to `dist/public`
- **Backend**: ESBuild bundles server code to `dist/index.js`
- **Deployment Target**: Autoscale deployment on Replit
- **Environment Variables**: OpenAI API key and database URL required

### Configuration
- **Database Migrations**: Drizzle Kit for schema management
- **TypeScript**: Shared configuration across client, server, and shared modules
- **Path Aliases**: Configured for clean imports across the application

## Changelog

```
Changelog:
- June 16, 2025. Initial setup
```

## User Preferences

```
Preferred communication style: Simple, everyday language.
```