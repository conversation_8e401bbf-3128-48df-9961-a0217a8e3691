import { pgTable, text, serial, integer, boolean, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const brandNames = pgTable("brand_names", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  keyword: text("keyword").notNull(),
  available: text("available").notNull(), // 'true', 'false', 'premium'
  letterCount: integer("letter_count").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertBrandNameSchema = createInsertSchema(brandNames).omit({
  id: true,
  createdAt: true,
});

export const generateNamesRequestSchema = z.object({
  keyword: z.string().min(1).max(100),
  style: z.string().optional(),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type BrandName = typeof brandNames.$inferSelect;
export type InsertBrandName = z.infer<typeof insertBrandNameSchema>;
export type GenerateNamesRequest = z.infer<typeof generateNamesRequestSchema>;
