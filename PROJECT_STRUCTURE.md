# 📁 BrandForge - Project Structure Documentation

## 🏗️ **Project Overview**
**BrandForge** - AI-Powered Brand Name Generator  
Full-Stack TypeScript Application with React Frontend & Express.js Backend

---

## 📂 **Root Directory Structure**

```
SmartScheduler/
├── 📁 client/                    # Frontend React application
├── 📁 server/                    # Backend Express.js API
├── 📁 shared/                    # Shared TypeScript schemas
├── 📁 attached_assets/           # Project documentation
├── 📁 node_modules/              # Dependencies (auto-generated)
├── 📄 package.json               # Project dependencies & scripts
├── 📄 package-lock.json          # Dependency lock file
├── 📄 tsconfig.json              # TypeScript configuration
├── 📄 vite.config.ts             # Vite build configuration
├── 📄 tailwind.config.ts         # Tailwind CSS configuration
├── 📄 components.json            # shadcn/ui configuration
├── 📄 drizzle.config.ts          # Database ORM configuration
├── 📄 postcss.config.js          # PostCSS configuration
├── 📄 replit.md                  # Project documentation
└── 📄 .replit                    # Replit deployment config
```

---

## 🎨 **Frontend Structure (client/)**

### **📁 Client Root**
```
client/
├── 📄 index.html                 # HTML entry point
├── 📁 public/                    # Static assets
└── 📁 src/                       # Source code
```

### **📁 Public Assets**
```
client/public/
└── 📁 logos/                     # Brand logo images
    ├── 🖼️ brand-logos-combined.png  # Combined logo image (current)
    ├── 🖼️ airbnb-logo.svg
    ├── 🖼️ google-logo.svg
    ├── 🖼️ google.png
    └── 🖼️ spotify-logo.svg
```

### **📁 Source Code Structure**
```
client/src/
├── 📄 main.tsx                   # React entry point
├── 📄 App.tsx                    # Main App component with routing
├── 📄 index.css                  # Global styles & Tailwind imports
├── 📁 components/                # React components
├── 📁 pages/                     # Page components
├── 📁 hooks/                     # Custom React hooks
└── 📁 lib/                       # Utility libraries
```

### **🧩 Components Directory**
```
src/components/
├── 📄 animated-background.tsx    # Background particle animations
├── 📄 brand-name-card.tsx        # Brand name display cards
├── 📄 brand-type-selector.tsx    # Style selection component
├── 📄 favorites-sidebar.tsx      # Favorites management sidebar
├── 📄 loading-skeleton.tsx       # Loading state skeletons
├── 📄 premium-hero.tsx           # Hero section component
├── 📄 saved-names-modal.tsx      # Modal for saved names
└── 📁 ui/                        # shadcn/ui component library
```

### **📁 UI Components (shadcn/ui)**
```
src/components/ui/
├── 📄 accordion.tsx              ├── 📄 navigation-menu.tsx
├── 📄 alert-dialog.tsx           ├── 📄 pagination.tsx
├── 📄 alert.tsx                  ├── 📄 popover.tsx
├── 📄 aspect-ratio.tsx           ├── 📄 progress.tsx
├── 📄 avatar.tsx                 ├── 📄 radio-group.tsx
├── 📄 badge.tsx                  ├── 📄 resizable.tsx
├── 📄 breadcrumb.tsx             ├── 📄 scroll-area.tsx
├── 📄 button.tsx                 ├── 📄 select.tsx
├── 📄 calendar.tsx               ├── 📄 separator.tsx
├── 📄 card.tsx                   ├── 📄 sheet.tsx
├── 📄 carousel.tsx               ├── 📄 sidebar.tsx
├── 📄 chart.tsx                  ├── 📄 skeleton.tsx
├── 📄 checkbox.tsx               ├── 📄 slider.tsx
├── 📄 collapsible.tsx            ├── 📄 switch.tsx
├── 📄 command.tsx                ├── 📄 table.tsx
├── 📄 context-menu.tsx           ├── 📄 tabs.tsx
├── 📄 dialog.tsx                 ├── 📄 textarea.tsx
├── 📄 drawer.tsx                 ├── 📄 toast.tsx
├── 📄 dropdown-menu.tsx          ├── 📄 toaster.tsx
├── 📄 form.tsx                   ├── 📄 toggle-group.tsx
├── 📄 hover-card.tsx             ├── 📄 toggle.tsx
├── 📄 input-otp.tsx              └── 📄 tooltip.tsx
├── 📄 input.tsx
├── 📄 label.tsx
└── 📄 menubar.tsx
```

### **📄 Pages Directory**
```
src/pages/
├── 📄 home.tsx                   # Main homepage (contains logo section)
└── 📄 not-found.tsx              # 404 error page
```

### **🔧 Hooks Directory**
```
src/hooks/
├── 📄 use-favorites.ts           # Favorites management logic
├── 📄 use-mobile.tsx             # Mobile device detection
├── 📄 use-scroll-reveal.ts       # Scroll-based animations
└── 📄 use-toast.ts               # Toast notification system
```

### **📚 Library Directory**
```
src/lib/
├── 📄 queryClient.ts             # TanStack Query configuration
├── 📄 types.ts                   # TypeScript type definitions
└── 📄 utils.ts                   # Utility functions (cn, etc.)
```

---

## 🖥️ **Backend Structure (server/)**

```
server/
├── 📄 index.ts                   # Express server entry point
├── 📄 routes.ts                  # API route definitions
├── 📄 storage.ts                 # Database operations & queries
├── 📄 vite.ts                    # Vite development server setup
└── 📁 services/
    └── 📄 openai.ts              # OpenAI API integration
```

---

## 🗄️ **Shared Resources**

### **📁 Shared Directory**
```
shared/
└── 📄 schema.ts                  # Database schemas & Zod validation
```

### **📁 Attached Assets**
```
attached_assets/
└── 📄 Pasted-You-are-a-senior-full-stack-developer-and-UX-UI-designer-Your-task-is-to-create-a-fully-functional-1750090774165_1750090774170.txt
```

---

## 🛠️ **Technology Stack**

### **📋 File Extensions & Types**
- **`.tsx`** - React TypeScript components
- **`.ts`** - TypeScript files
- **`.css`** - Stylesheets
- **`.html`** - HTML templates
- **`.json`** - Configuration files
- **`.js`** - JavaScript config files
- **`.svg`** - Vector graphics
- **`.png`** - Raster images
- **`.md`** - Documentation files

### **🔧 Core Technologies**
- **Frontend Framework**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + shadcn/ui
- **Animations**: Framer Motion
- **State Management**: TanStack Query + React Hooks
- **Routing**: Wouter (lightweight router)
- **Backend**: Node.js + Express.js + TypeScript
- **Database**: PostgreSQL + Drizzle ORM
- **AI Integration**: OpenAI GPT-4 API
- **Deployment**: Replit Platform

### **📦 Key Dependencies**
```json
{
  "react": "^18.x",
  "typescript": "^5.x", 
  "tailwindcss": "^3.x",
  "framer-motion": "^11.x",
  "express": "^4.x",
  "drizzle-orm": "^0.x",
  "openai": "^4.x",
  "vite": "^5.x",
  "@tanstack/react-query": "^5.x",
  "wouter": "^3.x"
}
```

---

## 🎯 **Current Working File**
**`client/src/pages/home.tsx`** - Main homepage component containing:
- Hero section with premium animations
- Brand name generator interface  
- **Logo display section** (combined brand logos)
- Responsive design with Tailwind CSS
- Framer Motion animations

---

## 📝 **Development Commands**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run check        # TypeScript type checking
npm run db:push      # Push database schema
```

---

## 🚀 **Architecture Highlights**
- **Full-Stack TypeScript** for type safety
- **Modern React** with hooks and functional components
- **Premium UI Design** with shadcn/ui components
- **Responsive Design** with Tailwind CSS
- **Smooth Animations** with Framer Motion
- **Type-Safe Database** with Drizzle ORM
- **AI Integration** with OpenAI GPT-4
- **Fast Development** with Vite hot reload
- **Production Ready** deployment on Replit

This is a **professional, scalable, and modern web application** with excellent architecture! 🎉
