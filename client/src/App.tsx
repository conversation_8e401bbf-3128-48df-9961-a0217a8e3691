import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Home from "@/pages/home";
import NotFound from "@/pages/not-found";

// Lazy load pages for better performance
import { lazy, Suspense } from "react";

const Product = lazy(() => import("@/pages/product"));
const Features = lazy(() => import("@/pages/features"));
const Pricing = lazy(() => import("@/pages/pricing"));
const ApiAccess = lazy(() => import("@/pages/api-access"));
const BulkGeneration = lazy(() => import("@/pages/bulk-generation"));
const Support = lazy(() => import("@/pages/support"));
const Help = lazy(() => import("@/pages/help"));
const Contact = lazy(() => import("@/pages/contact"));
const Privacy = lazy(() => import("@/pages/privacy"));
const Terms = lazy(() => import("@/pages/terms"));

function Router() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <Switch>
        {/* Marketing Pages */}
        <Route path="/" component={Home} />
        <Route path="/product" component={Product} />
        <Route path="/features" component={Features} />
        <Route path="/pricing" component={Pricing} />

        {/* Developer Pages */}
        <Route path="/api" component={ApiAccess} />
        <Route path="/bulk" component={BulkGeneration} />

        {/* Support Pages */}
        <Route path="/support" component={Support} />
        <Route path="/help" component={Help} />
        <Route path="/contact" component={Contact} />

        {/* Legal Pages */}
        <Route path="/privacy" component={Privacy} />
        <Route path="/terms" component={Terms} />

        {/* 404 Page */}
        <Route component={NotFound} />
      </Switch>
    </Suspense>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
