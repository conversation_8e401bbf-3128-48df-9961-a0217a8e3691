import { useState, useEffect } from "react";
import type { SavedName, BrandName } from "@/lib/types";

export function useFavorites() {
  const [savedNames, setSavedNames] = useState<SavedName[]>([]);

  useEffect(() => {
    const saved = localStorage.getItem("savedNames");
    if (saved) {
      try {
        setSavedNames(JSON.parse(saved));
      } catch (error) {
        console.error("Error parsing saved names:", error);
        setSavedNames([]);
      }
    }
  }, []);

  const saveName = (brandName: BrandName) => {
    const savedName: SavedName = {
      name: brandName.name,
      description: brandName.description,
      keyword: brandName.keyword,
      available: brandName.available,
      letterCount: brandName.letterCount,
      savedAt: new Date().toISOString()
    };

    const newSavedNames = [...savedNames, savedName];
    setSavedNames(newSavedNames);
    localStorage.setItem("savedNames", JSON.stringify(newSavedNames));
  };

  const removeName = (name: string) => {
    const newSavedNames = savedNames.filter(saved => saved.name !== name);
    setSavedNames(newSavedNames);
    localStorage.setItem("savedNames", JSON.stringify(newSavedNames));
  };

  const isNameSaved = (name: string) => {
    return savedNames.some(saved => saved.name === name);
  };

  const clearAll = () => {
    setSavedNames([]);
    localStorage.removeItem("savedNames");
  };

  const exportCSV = () => {
    if (savedNames.length === 0) return;
    
    const csvContent = "Name,Description,Keyword,Available,Letter Count,Domain Link,Logo Link\n" +
      savedNames.map(name => 
        `"${name.name}","${name.description}","${name.keyword}","${name.available}",${name.letterCount},"https://www.namecheap.com/domains/registration/results/?domain=${name.name.toLowerCase()}.com","https://brandmark.io/logo-design/?name=${name.name.toLowerCase()}"`
      ).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'saved-brand-names.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return {
    savedNames,
    saveName,
    removeName,
    isNameSaved,
    clearAll,
    exportCSV,
    count: savedNames.length
  };
}
