import { motion } from 'framer-motion';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react';

export function LoadingSkeleton() {
  const loadingMessages = [
    "Analyzing your keyword...",
    "Generating creative combinations...",
    "Checking domain availability...",
    "Crafting brandable names...",
    "Almost ready!"
  ];

  return (
    <section className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Premium Loading Header */}
      <div className="text-center mb-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center gap-3 mb-6"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="p-3 bg-gradient-to-r from-primary-500 to-accent-purple rounded-full"
          >
            <Brain className="text-white" size={24} />
          </motion.div>
          <div>
            <h2 className="text-3xl font-bold text-gray-900">Generating Your Brand Names</h2>
            <motion.p
              key={Math.floor(Date.now() / 2000) % loadingMessages.length}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-gray-600 mt-2"
            >
              {loadingMessages[Math.floor(Date.now() / 2000) % loadingMessages.length]}
            </motion.p>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="w-full max-w-md mx-auto bg-gray-200 rounded-full h-2 mb-8">
          <motion.div
            className="bg-gradient-to-r from-primary-500 to-accent-purple h-2 rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ duration: 3, ease: "easeInOut" }}
          />
        </div>
      </div>

      {/* Animated Skeleton Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className="bg-white shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-6">
                <motion.div
                  className="h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg mb-4"
                  animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  style={{ backgroundSize: '200% 100%' }}
                />
                <motion.div
                  className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded mb-4 w-3/4"
                  animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear", delay: 0.2 }}
                  style={{ backgroundSize: '200% 100%' }}
                />
                <div className="flex space-x-3">
                  <motion.div
                    className="h-10 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg flex-1"
                    animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear", delay: 0.4 }}
                    style={{ backgroundSize: '200% 100%' }}
                  />
                  <motion.div
                    className="h-10 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg flex-1"
                    animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear", delay: 0.6 }}
                    style={{ backgroundSize: '200% 100%' }}
                  />
                  <motion.div
                    className="h-10 w-10 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"
                    animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear", delay: 0.8 }}
                    style={{ backgroundSize: '200% 100%' }}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Fun Facts */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1 }}
        className="text-center mt-12"
      >
        <div className="flex items-center justify-center gap-2 text-gray-500">
          <Sparkles size={16} />
          <span className="text-sm">Did you know? The best brand names are usually 5-7 letters long</span>
          <Zap size={16} />
        </div>
      </motion.div>
    </section>
  );
}
