import { motion } from "framer-motion";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, Globe, Palette, DollarSign, Award, Tag, Copy, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import type { BrandName } from "@/lib/types";

interface BrandNameCardProps {
  brandName: BrandName;
  isSaved: boolean;
  onToggleFavorite: (brandName: BrandName) => void;
  index?: number;
}

export function BrandNameCard({ brandName, isSaved, onToggleFavorite, index = 0 }: BrandNameCardProps) {
  const { toast } = useToast();
  const domainExtension = brandName.domainType || 'com';
  const domainUrl = `https://www.namecheap.com/domains/registration/results/?domain=${brandName.name.toLowerCase()}.${domainExtension}`;
  const logoUrl = `https://brandmark.io/logo-design/?name=${brandName.name.toLowerCase()}`;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(brandName.name);
      toast({
        title: "Copied!",
        description: `"${brandName.name}" copied to clipboard`,
      });
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const getAvailabilityColor = (available: string) => {
    switch (available) {
      case 'true':
        return 'text-green-600';
      case 'false':
        return 'text-red-600';
      case 'premium':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const getAvailabilityText = (available: string) => {
    switch (available) {
      case 'true':
        return 'Available';
      case 'false':
        return 'Taken';
      case 'premium':
        return 'Premium';
      default:
        return 'Unknown';
    }
  };

  const getBrandabilityColor = (brandability: string) => {
    switch (brandability) {
      case 'excellent':
        return 'bg-green-100 text-green-800';
      case 'good':
        return 'bg-blue-100 text-blue-800';
      case 'fair':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStyleInfo = (keyword: string) => {
    // This would ideally come from the API response, but for now we'll infer from the name
    const name = brandName.name.toLowerCase();
    if (name.length <= 5) return { type: "Short & Catchy", color: "bg-yellow-100 text-yellow-800" };
    if (name.includes('ly') || name.includes('io') || name.includes('ify')) return { type: "Trendy Startup", color: "bg-indigo-100 text-indigo-800" };
    if (name.includes(' ') || /[A-Z]/.test(brandName.name.slice(1))) return { type: "Compound", color: "bg-green-100 text-green-800" };
    if (/^[a-z]+$/.test(name) && name.length > 5) return { type: "Real Word", color: "bg-blue-100 text-blue-800" };
    if (name.includes('aur') || name.includes('lux') || name.includes('val')) return { type: "Luxury", color: "bg-amber-100 text-amber-800" };
    return { type: "Invented", color: "bg-purple-100 text-purple-800" };
  };

  const styleInfo = getStyleInfo(brandName.keyword);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.5,
        delay: index * 0.1,
        ease: "easeOut"
      }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className="group"
    >
      <Card className="relative bg-white shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-100 overflow-hidden">
        {/* Gradient overlay on hover */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-accent-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        <CardContent className="relative p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                {brandName.name}
              </h3>
              <Badge variant="outline" className="text-xs border-gray-300">
                .{domainExtension}
              </Badge>
              <Badge className={cn("text-xs font-medium", styleInfo.color)}>
                {styleInfo.type}
              </Badge>
            </div>
            <p className="text-sm text-gray-600 mb-3 leading-relaxed">{brandName.description}</p>
            
            {/* Professional Domain Information */}
            <div className="space-y-2">
              {brandName.category && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <Tag size={12} />
                  <span>{brandName.category}</span>
                </div>
              )}
              
              {brandName.domainPrice && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <DollarSign size={12} />
                  <span>{brandName.domainPrice}</span>
                </div>
              )}
              
              {brandName.brandability && (
                <div className="flex items-center gap-1">
                  <Award size={12} className="text-gray-400" />
                  <Badge 
                    variant="secondary" 
                    className={cn("text-xs", getBrandabilityColor(brandName.brandability))}
                  >
                    {brandName.brandability} brandability
                  </Badge>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={copyToClipboard}
              className="text-gray-400 hover:text-primary-500 transition-colors p-2"
            >
              <Copy size={16} />
            </Button>
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleFavorite(brandName)}
                className={cn(
                  "text-gray-400 hover:text-red-500 transition-colors p-2",
                  isSaved && "text-red-500 hover:text-red-600"
                )}
              >
                <motion.div
                  animate={{ scale: isSaved ? [1, 1.2, 1] : 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Heart className={cn("text-lg", isSaved && "fill-current")} size={18} />
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-3">
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                asChild
                className="w-full bg-primary-500 hover:bg-primary-600 text-white font-medium shadow-sm hover:shadow-md transition-all duration-200"
                size="sm"
              >
                <a href={domainUrl} target="_blank" rel="noopener noreferrer">
                  <Globe className="mr-2" size={16} />
                  Check Domain
                  <ExternalLink className="ml-1" size={12} />
                </a>
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                asChild
                variant="secondary"
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium shadow-sm hover:shadow-md transition-all duration-200"
                size="sm"
              >
                <a href={logoUrl} target="_blank" rel="noopener noreferrer">
                  <Palette className="mr-2" size={16} />
                  Make Logo
                  <ExternalLink className="ml-1" size={12} />
                </a>
              </Button>
            </motion.div>
          </div>

          <div className="flex justify-between items-center text-xs">
            <div className="flex items-center gap-3 text-gray-500">
              <span className="flex items-center gap-1">
                <Tag size={12} />
                {brandName.letterCount} letters
              </span>
              <span className={cn("font-medium", brandName.letterCount <= 8 ? 'text-green-600' : 'text-amber-600')}>
                {brandName.letterCount <= 8 ? 'Short' : 'Long'}
              </span>
            </div>
            <div className={cn("font-semibold px-2 py-1 rounded-full text-xs",
              brandName.available === 'true' ? 'bg-green-100 text-green-700' :
              brandName.available === 'false' ? 'bg-red-100 text-red-700' :
              'bg-yellow-100 text-yellow-700'
            )}>
              {getAvailabilityText(brandName.available)}
            </div>
          </div>
        </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
