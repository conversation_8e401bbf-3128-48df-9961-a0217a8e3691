import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, Globe, Palette, DollarSign, Award, Tag } from "lucide-react";
import { cn } from "@/lib/utils";
import type { BrandName } from "@/lib/types";

interface BrandNameCardProps {
  brandName: BrandName;
  isSaved: boolean;
  onToggleFavorite: (brandName: BrandName) => void;
}

export function BrandNameCard({ brandName, isSaved, onToggleFavorite }: BrandNameCardProps) {
  const domainExtension = brandName.domainType || 'com';
  const domainUrl = `https://www.namecheap.com/domains/registration/results/?domain=${brandName.name.toLowerCase()}.${domainExtension}`;
  const logoUrl = `https://brandmark.io/logo-design/?name=${brandName.name.toLowerCase()}`;

  const getAvailabilityColor = (available: string) => {
    switch (available) {
      case 'true':
        return 'text-green-600';
      case 'false':
        return 'text-red-600';
      case 'premium':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const getAvailabilityText = (available: string) => {
    switch (available) {
      case 'true':
        return 'Available';
      case 'false':
        return 'Taken';
      case 'premium':
        return 'Premium';
      default:
        return 'Unknown';
    }
  };

  const getBrandabilityColor = (brandability: string) => {
    switch (brandability) {
      case 'excellent':
        return 'bg-green-100 text-green-800';
      case 'good':
        return 'bg-blue-100 text-blue-800';
      case 'fair':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="bg-white shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 animate-slide-up">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-xl font-bold text-gray-900">{brandName.name}</h3>
              <Badge variant="outline" className="text-xs">
                .{domainExtension}
              </Badge>
            </div>
            <p className="text-sm text-gray-500 mb-3">{brandName.description}</p>
            
            {/* Professional Domain Information */}
            <div className="space-y-2">
              {brandName.category && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <Tag size={12} />
                  <span>{brandName.category}</span>
                </div>
              )}
              
              {brandName.domainPrice && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <DollarSign size={12} />
                  <span>{brandName.domainPrice}</span>
                </div>
              )}
              
              {brandName.brandability && (
                <div className="flex items-center gap-1">
                  <Award size={12} className="text-gray-400" />
                  <Badge 
                    variant="secondary" 
                    className={cn("text-xs", getBrandabilityColor(brandName.brandability))}
                  >
                    {brandName.brandability} brandability
                  </Badge>
                </div>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleFavorite(brandName)}
            className={cn(
              "text-gray-400 hover:text-red-500 transition-colors p-1 ml-2",
              isSaved && "text-red-500 hover:text-red-600"
            )}
          >
            <Heart className={cn("text-lg", isSaved && "fill-current")} size={18} />
          </Button>
        </div>
        
        <div className="space-y-3">
          <div className="flex space-x-3">
            <Button
              asChild
              className="flex-1 bg-primary text-primary-foreground hover:bg-primary/90"
              size="sm"
            >
              <a href={domainUrl} target="_blank" rel="noopener noreferrer">
                <Globe className="mr-2" size={16} />
                Check .{domainExtension}
              </a>
            </Button>
            <Button
              asChild
              variant="secondary"
              className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
              size="sm"
            >
              <a href={logoUrl} target="_blank" rel="noopener noreferrer">
                <Palette className="mr-2" size={16} />
                Make Logo
              </a>
            </Button>
          </div>
          <div className="flex justify-between text-xs text-gray-500">
            <span>{brandName.letterCount} letters • {brandName.letterCount <= 8 ? 'Short' : 'Long'}</span>
            <span className={cn("font-medium", getAvailabilityColor(brandName.available))}>
              {getAvailabilityText(brandName.available)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
