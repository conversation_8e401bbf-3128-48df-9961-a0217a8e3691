import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Heart, 
  X, 
  Download, 
  Search, 
  Trash2, 
  Filter,
  Copy,
  ExternalLink,
  Globe,
  Palette
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import type { SavedName } from '@/lib/types';

interface FavoritesSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  savedNames: SavedName[];
  onRemove: (name: string) => void;
  onClearAll: () => void;
  onExportCSV: () => void;
}

export function FavoritesSidebar({ 
  isOpen, 
  onClose, 
  savedNames, 
  onRemove, 
  onClearAll, 
  onExportCSV 
}: FavoritesSidebarProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const { toast } = useToast();

  const filteredNames = savedNames.filter(name => {
    const matchesSearch = name.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         name.keyword.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (filterType === 'all') return matchesSearch;
    if (filterType === 'available') return matchesSearch && name.available === 'true';
    if (filterType === 'premium') return matchesSearch && name.available === 'premium';
    if (filterType === 'short') return matchesSearch && name.letterCount <= 8;
    
    return matchesSearch;
  });

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: `"${text}" copied to clipboard`,
      });
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const filterOptions = [
    { value: 'all', label: 'All Names', count: savedNames.length },
    { value: 'available', label: 'Available', count: savedNames.filter(n => n.available === 'true').length },
    { value: 'premium', label: 'Premium', count: savedNames.filter(n => n.available === 'premium').length },
    { value: 'short', label: 'Short (≤8)', count: savedNames.filter(n => n.letterCount <= 8).length },
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          />

          {/* Sidebar */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-2xl z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-primary-50 to-purple-50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary-500 rounded-lg">
                  <Heart className="text-white fill-current" size={20} />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Saved Names</h2>
                  <p className="text-sm text-gray-600">{savedNames.length} favorites</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </Button>
            </div>

            {/* Search and Filters */}
            <div className="p-4 border-b border-gray-100 space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="Search saved names..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-gray-50 border-gray-200"
                />
              </div>

              <div className="flex flex-wrap gap-2">
                {filterOptions.map((option) => (
                  <Button
                    key={option.value}
                    variant={filterType === option.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterType(option.value)}
                    className={cn(
                      "text-xs",
                      filterType === option.value 
                        ? "bg-primary-500 text-white" 
                        : "text-gray-600 hover:text-gray-900"
                    )}
                  >
                    {option.label}
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {option.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>

            {/* Names List */}
            <div className="flex-1 overflow-y-auto p-4">
              {filteredNames.length === 0 ? (
                <div className="text-center py-12">
                  <Heart className="mx-auto text-gray-300 mb-4" size={48} />
                  <p className="text-gray-500 mb-2">
                    {searchTerm ? 'No names match your search' : 'No saved names yet'}
                  </p>
                  <p className="text-sm text-gray-400">
                    {searchTerm ? 'Try a different search term' : 'Start generating names and save your favorites!'}
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  <AnimatePresence>
                    {filteredNames.map((name, index) => (
                      <motion.div
                        key={name.name}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, x: 100 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="group bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 text-lg">{name.name}</h3>
                            <p className="text-sm text-gray-600 mb-2">{name.description}</p>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <span>{name.letterCount} letters</span>
                              <span>•</span>
                              <span className="capitalize">{name.keyword}</span>
                              <Badge 
                                variant="outline" 
                                className={cn(
                                  "text-xs",
                                  name.available === 'true' ? 'border-green-500 text-green-700' :
                                  name.available === 'false' ? 'border-red-500 text-red-700' :
                                  'border-yellow-500 text-yellow-700'
                                )}
                              >
                                {name.available === 'true' ? 'Available' : 
                                 name.available === 'false' ? 'Taken' : 'Premium'}
                              </Badge>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onRemove(name.name)}
                            className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all duration-200"
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(name.name)}
                            className="flex-1 text-xs"
                          >
                            <Copy size={12} className="mr-1" />
                            Copy
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                            className="flex-1 text-xs"
                          >
                            <a 
                              href={`https://www.namecheap.com/domains/registration/results/?domain=${name.name.toLowerCase()}.com`}
                              target="_blank" 
                              rel="noopener noreferrer"
                            >
                              <Globe size={12} className="mr-1" />
                              Domain
                              <ExternalLink size={10} className="ml-1" />
                            </a>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                            className="flex-1 text-xs"
                          >
                            <a 
                              href={`https://brandmark.io/logo-design/?name=${name.name.toLowerCase()}`}
                              target="_blank" 
                              rel="noopener noreferrer"
                            >
                              <Palette size={12} className="mr-1" />
                              Logo
                              <ExternalLink size={10} className="ml-1" />
                            </a>
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              )}
            </div>

            {/* Footer Actions */}
            {savedNames.length > 0 && (
              <div className="p-4 border-t border-gray-200 bg-gray-50 space-y-3">
                <Button
                  onClick={onExportCSV}
                  className="w-full bg-primary-500 hover:bg-primary-600 text-white"
                >
                  <Download className="mr-2" size={16} />
                  Export as CSV
                </Button>
                <Button
                  variant="outline"
                  onClick={onClearAll}
                  className="w-full text-red-600 border-red-200 hover:bg-red-50"
                >
                  <Trash2 className="mr-2" size={16} />
                  Clear All ({savedNames.length})
                </Button>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
