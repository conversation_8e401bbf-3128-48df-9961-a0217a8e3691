import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Heart, Globe, Palette, Download, Trash2, HeartCrack } from "lucide-react";
import type { SavedName } from "@/lib/types";

interface SavedNamesModalProps {
  isOpen: boolean;
  onClose: () => void;
  savedNames: SavedName[];
  onRemove: (name: string) => void;
  onClearAll: () => void;
  onExportCSV: () => void;
}

export function SavedNamesModal({ 
  isOpen, 
  onClose, 
  savedNames, 
  onRemove, 
  onClearAll, 
  onExportCSV 
}: SavedNamesModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Heart className="text-red-500 fill-current" size={24} />
            Saved Names
          </DialogTitle>
        </DialogHeader>
        
        {savedNames.length === 0 ? (
          <div className="text-center py-12">
            <HeartCrack className="mx-auto text-gray-300 mb-4" size={48} />
            <p className="text-gray-500 text-lg">No saved names yet</p>
            <p className="text-gray-400 text-sm">Start generating names and save your favorites!</p>
          </div>
        ) : (
          <>
            <ScrollArea className="max-h-96">
              <div className="space-y-4">
                {savedNames.map((name) => (
                  <div key={name.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-semibold text-gray-900">{name.name}</h4>
                      <p className="text-sm text-gray-500">{name.description}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {name.letterCount} letters • Keyword: {name.keyword}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        asChild
                        variant="ghost"
                        size="sm"
                        className="text-primary hover:text-primary/80"
                      >
                        <a 
                          href={`https://www.namecheap.com/domains/registration/results/?domain=${name.name.toLowerCase()}.com`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Globe size={16} />
                        </a>
                      </Button>
                      <Button
                        asChild
                        variant="ghost"
                        size="sm"
                        className="text-purple-600 hover:text-purple-700"
                      >
                        <a 
                          href={`https://brandmark.io/logo-design/?name=${name.name.toLowerCase()}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Palette size={16} />
                        </a>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onRemove(name.name)}
                        className="text-red-500 hover:text-red-600"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            <div className="flex justify-between items-center pt-4 border-t">
              <Button 
                variant="outline"
                onClick={onExportCSV}
                className="text-primary hover:text-primary/80"
              >
                <Download className="mr-2" size={16} />
                Export CSV
              </Button>
              <Button 
                variant="destructive"
                onClick={onClearAll}
              >
                <Trash2 className="mr-2" size={16} />
                Clear All
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
