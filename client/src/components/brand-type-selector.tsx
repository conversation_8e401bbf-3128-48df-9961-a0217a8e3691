import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Zap, 
  BookOpen, 
  Layers, 
  Sparkles, 
  TrendingUp, 
  Crown,
  ArrowRight,
  Check
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface BrandStyle {
  id: string;
  title: string;
  description: string;
  examples: string;
  icon: React.ComponentType<any>;
  color: string;
  gradient: string;
  features: string[];
}

const brandStyles: BrandStyle[] = [
  {
    id: 'short-catchy',
    title: 'Short & Catchy',
    description: 'Ultra-short, memorable names perfect for modern brands',
    examples: 'Flux, Peak, Vibe, Spark',
    icon: Zap,
    color: 'text-yellow-600',
    gradient: 'from-yellow-500 to-orange-500',
    features: ['1-word names', '4-7 letters', 'High recall', 'Premium domains']
  },
  {
    id: 'real-words',
    title: 'Real Words',
    description: 'Existing English words with creative usage',
    examples: 'Horizon, Quest, Summit, Pulse',
    icon: BookOpen,
    color: 'text-blue-600',
    gradient: 'from-blue-500 to-cyan-500',
    features: ['Dictionary words', 'Clear meaning', 'Easy to spell', 'Professional']
  },
  {
    id: 'compound',
    title: 'Compound Words',
    description: 'Two powerful words combined for impact',
    examples: 'PowerCore, NextWave, FlexFlow',
    icon: Layers,
    color: 'text-green-600',
    gradient: 'from-green-500 to-emerald-500',
    features: ['Descriptive', 'Brandable', 'Memorable', 'Scalable']
  },
  {
    id: 'invented',
    title: 'Made-up / Invented',
    description: 'Unique invented names that stand out',
    examples: 'Zephyra, Nexova, Lumira, Axira',
    icon: Sparkles,
    color: 'text-purple-600',
    gradient: 'from-purple-500 to-pink-500',
    features: ['100% unique', 'Trademark-friendly', 'Global appeal', 'Future-proof']
  },
  {
    id: 'trendy-startup',
    title: 'Trendy Startup-style',
    description: 'Modern tech-style names with trendy suffixes',
    examples: 'Buildly, Streamio, Growify',
    icon: TrendingUp,
    color: 'text-indigo-600',
    gradient: 'from-indigo-500 to-blue-500',
    features: ['Tech-savvy', 'Startup vibe', 'Scalable', 'Investor-friendly']
  },
  {
    id: 'luxury-premium',
    title: 'Luxury / Premium',
    description: 'Sophisticated names with high-end appeal',
    examples: 'Aurelia, Meridian, Valence',
    icon: Crown,
    color: 'text-amber-600',
    gradient: 'from-amber-500 to-yellow-500',
    features: ['Sophisticated', 'Premium feel', 'Elegant sound', 'High-value']
  }
];

interface BrandTypeSelectorProps {
  onStyleSelect: (styleId: string) => void;
  selectedStyle?: string;
  isGenerating?: boolean;
}

export function BrandTypeSelector({ onStyleSelect, selectedStyle, isGenerating }: BrandTypeSelectorProps) {
  const [hoveredStyle, setHoveredStyle] = useState<string | null>(null);

  return (
    <div className="w-full max-w-6xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Choose Your Brand Name Style
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Select the style that best fits your vision. Each style is crafted to create 
          memorable, brandable names that resonate with your target audience.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {brandStyles.map((style, index) => {
            const Icon = style.icon;
            const isSelected = selectedStyle === style.id;
            const isHovered = hoveredStyle === style.id;

            return (
              <motion.div
                key={style.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                onHoverStart={() => setHoveredStyle(style.id)}
                onHoverEnd={() => setHoveredStyle(null)}
              >
                <Card 
                  className={cn(
                    "relative overflow-hidden cursor-pointer transition-all duration-300 border-2 h-full",
                    isSelected 
                      ? "border-primary-500 shadow-glow bg-primary-50/50" 
                      : "border-gray-200 hover:border-primary-300 hover:shadow-lg",
                    isGenerating && "pointer-events-none opacity-75"
                  )}
                  onClick={() => !isGenerating && onStyleSelect(style.id)}
                >
                  {/* Gradient overlay on hover */}
                  <motion.div
                    className={cn(
                      "absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-300",
                      style.gradient
                    )}
                    animate={{ opacity: isHovered || isSelected ? 0.05 : 0 }}
                  />

                  <CardContent className="relative p-6 h-full flex flex-col">
                    {/* Header with icon and title */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className={cn(
                          "p-2 rounded-lg transition-colors duration-300",
                          isSelected ? "bg-primary-500 text-white" : "bg-gray-100 text-gray-600"
                        )}>
                          <Icon size={20} />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg">
                            {style.title}
                          </h3>
                          {isSelected && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="flex items-center gap-1 text-primary-600 text-sm font-medium"
                            >
                              <Check size={14} />
                              Selected
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-gray-600 text-sm mb-4 flex-grow">
                      {style.description}
                    </p>

                    {/* Features */}
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {style.features.map((feature, idx) => (
                          <Badge 
                            key={idx}
                            variant="secondary" 
                            className="text-xs bg-gray-100 text-gray-700"
                          >
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Examples */}
                    <div className="mb-4">
                      <p className="text-xs text-gray-500 mb-2 font-medium">Examples:</p>
                      <p className="text-sm text-gray-700 font-medium">
                        {style.examples}
                      </p>
                    </div>

                    {/* Action button */}
                    <motion.div
                      animate={{ 
                        scale: isSelected ? 1.02 : 1,
                        opacity: isSelected ? 1 : 0.8
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <Button
                        className={cn(
                          "w-full transition-all duration-300",
                          isSelected 
                            ? "bg-primary-500 hover:bg-primary-600 text-white" 
                            : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                        )}
                        disabled={isGenerating}
                      >
                        {isSelected ? (
                          <>
                            <Check className="mr-2" size={16} />
                            Generate Names
                          </>
                        ) : (
                          <>
                            Select Style
                            <ArrowRight className="ml-2" size={16} />
                          </>
                        )}
                      </Button>
                    </motion.div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    </div>
  );
}
