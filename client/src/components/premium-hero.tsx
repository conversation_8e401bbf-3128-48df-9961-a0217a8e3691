import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, Sparkles, Zap, TrendingUp, ArrowRight } from 'lucide-react';
import { AnimatedBackground, GlassMorphismOrbs } from './animated-background';
import { cn } from '@/lib/utils';

interface PremiumHeroProps {
  keyword: string;
  onKeywordChange: (keyword: string) => void;
  onGenerate: () => void;
  isGenerating?: boolean;
}

export function PremiumHero({ keyword, onKeywordChange, onGenerate, isGenerating }: PremiumHeroProps) {
  const [isFocused, setIsFocused] = useState(false);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isGenerating) {
      onGenerate();
    }
  };

  const features = [
    { icon: Sparkles, text: "AI-Powered Generation", color: "text-yellow-600" },
    { icon: Zap, text: "Instant Results", color: "text-blue-600" },
    { icon: TrendingUp, text: "Premium Quality", color: "text-green-600" }
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 gradient-bg">
        <AnimatedBackground />
        <GlassMorphismOrbs />
      </div>

      {/* Content */}
      <div className="relative z-10 w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="mb-8"
        >
          {/* Premium Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 mb-6"
          >
            <Badge 
              variant="secondary" 
              className="glass text-primary-600 border-primary-200 px-4 py-2 text-sm font-medium"
            >
              <Sparkles className="mr-2" size={16} />
              AI-Powered Brand Generator
            </Badge>
          </motion.div>

          {/* Main Headline */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Generate the Perfect{" "}
            <span className="relative">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-500 via-accent-purple to-primary-600">
                Brand Name
              </span>
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-purple rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              />
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Create unique, memorable, and brandable names for your startup or product in seconds. 
            Powered by advanced AI and designed for modern entrepreneurs.
          </motion.p>
        </motion.div>

        {/* Search Interface */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="max-w-2xl mx-auto mb-12"
        >
          <div className="relative group">
            {/* Glow effect */}
            <div className={cn(
              "absolute -inset-1 bg-gradient-to-r from-primary-500 to-accent-purple rounded-2xl blur opacity-0 group-hover:opacity-20 transition-opacity duration-300",
              isFocused && "opacity-30"
            )} />
            
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                <Search className={cn(
                  "text-gray-400 transition-colors duration-300",
                  isFocused && "text-primary-500"
                )} size={24} />
              </div>
              
              <Input
                type="text"
                value={keyword}
                onChange={(e) => onKeywordChange(e.target.value)}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                onKeyPress={handleKeyPress}
                placeholder="Enter your keyword or niche (e.g., tech, fitness, food)"
                className="w-full pl-16 pr-40 py-6 text-lg bg-white/80 backdrop-blur-sm border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-300 placeholder:text-gray-400"
                disabled={isGenerating}
              />
              
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <Button 
                  onClick={onGenerate}
                  disabled={isGenerating || !keyword.trim()}
                  className="btn-premium h-12 px-8 text-base font-semibold"
                >
                  {isGenerating ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="mr-2"
                      >
                        <Sparkles size={20} />
                      </motion.div>
                      Generating...
                    </>
                  ) : (
                    <>
                      Generate
                      <ArrowRight className="ml-2" size={20} />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Feature Pills */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-wrap justify-center gap-4 mt-6"
          >
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
                  className="flex items-center gap-2 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-gray-200"
                >
                  <Icon className={cn("w-4 h-4", feature.color)} />
                  <span className="text-sm font-medium text-gray-700">
                    {feature.text}
                  </span>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="text-center"
        >
          <p className="text-gray-500 text-sm mb-4">
            Trusted by 10,000+ entrepreneurs and startups worldwide
          </p>
          <div className="flex justify-center items-center gap-8 opacity-60">
            {/* Placeholder for company logos */}
            <div className="w-20 h-8 bg-gray-300 rounded opacity-50" />
            <div className="w-24 h-8 bg-gray-300 rounded opacity-50" />
            <div className="w-16 h-8 bg-gray-300 rounded opacity-50" />
            <div className="w-22 h-8 bg-gray-300 rounded opacity-50" />
          </div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="w-1 h-3 bg-gray-400 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
}
