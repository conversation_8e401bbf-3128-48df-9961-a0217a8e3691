export interface BrandName {
  id: number;
  name: string;
  description: string;
  keyword: string;
  available: string;
  letterCount: number;
  domainType?: string;
  domainPrice?: string;
  category?: string;
  brandability?: string;
  createdAt?: Date;
}

export interface SavedName {
  name: string;
  description: string;
  keyword: string;
  available: string;
  letterCount: number;
  savedAt: string;
}

export interface GenerateNamesResponse {
  names: BrandName[];
  keyword: string;
  count: number;
}
