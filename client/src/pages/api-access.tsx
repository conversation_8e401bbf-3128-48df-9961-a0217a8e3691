import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { 
  <PERSON>, 
  Key, 
  Co<PERSON>, 
  Eye, 
  EyeOff,
  Plus,
  Trash2,
  ArrowRight,
  Sparkles,
  Terminal,
  Book,
  Zap,
  CheckCircle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

const ApiAccess = () => {
  const [showApi<PERSON>ey, setShowApi<PERSON>ey] = useState(false);
  const [new<PERSON><PERSON><PERSON><PERSON>, setN<PERSON><PERSON><PERSON><PERSON><PERSON>] = useState("");

  const apiKeys = [
    {
      id: 1,
      name: "Production App",
      key: "bf_live_sk_1234567890abcdef",
      created: "2024-01-15",
      lastUsed: "2024-01-20",
      requests: 1250,
      limit: 10000
    },
    {
      id: 2,
      name: "Development",
      key: "bf_test_sk_abcdef1234567890",
      created: "2024-01-10",
      lastUsed: "2024-01-19",
      requests: 450,
      limit: 10000
    }
  ];

  const endpoints = [
    {
      method: "POST",
      path: "/api/v1/generate",
      description: "Generate brand names based on keywords and style preferences"
    },
    {
      method: "GET",
      path: "/api/v1/domains/check",
      description: "Check domain availability for generated names"
    },
    {
      method: "POST",
      path: "/api/v1/bulk/generate",
      description: "Generate multiple brand names in batch"
    },
    {
      method: "GET",
      path: "/api/v1/usage",
      description: "Get current API usage statistics"
    }
  ];

  const codeExamples = {
    curl: `curl -X POST https://api.brandforge.ai/v1/generate \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "keywords": ["tech", "innovation"],
    "style": "modern",
    "industry": "technology",
    "count": 10
  }'`,
    javascript: `const response = await fetch('https://api.brandforge.ai/v1/generate', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    keywords: ['tech', 'innovation'],
    style: 'modern',
    industry: 'technology',
    count: 10
  })
});

const data = await response.json();
console.log(data.names);`,
    python: `import requests

url = "https://api.brandforge.ai/v1/generate"
headers = {
    "Authorization": "Bearer YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "keywords": ["tech", "innovation"],
    "style": "modern",
    "industry": "technology",
    "count": 10
}

response = requests.post(url, headers=headers, json=data)
names = response.json()["names"]
print(names)`
  };

  const responseExample = `{
  "success": true,
  "names": [
    {
      "name": "TechFlow",
      "score": 0.95,
      "style": "modern",
      "domain_available": true,
      "trademark_risk": "low"
    },
    {
      "name": "InnovateLab",
      "score": 0.92,
      "style": "modern", 
      "domain_available": false,
      "trademark_risk": "medium"
    }
  ],
  "usage": {
    "requests_used": 1251,
    "requests_remaining": 8749
  }
}`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <Code className="w-4 h-4 mr-2" />
                Developer API
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                Integrate BrandForge{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Into Your Apps
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                Powerful REST API to integrate AI-powered brand name generation 
                directly into your applications, websites, and workflows.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <Key className="w-5 h-5 mr-2" />
                  Get API Key
                </Button>
                <Button size="lg" variant="outline">
                  <Book className="w-5 h-5 mr-2" />
                  View Documentation
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* API Management */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <Tabs defaultValue="keys" className="space-y-8">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="keys">API Keys</TabsTrigger>
                <TabsTrigger value="docs">Documentation</TabsTrigger>
                <TabsTrigger value="examples">Code Examples</TabsTrigger>
              </TabsList>

              {/* API Keys Tab */}
              <TabsContent value="keys" className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Key className="w-5 h-5 mr-2" />
                        API Key Management
                      </CardTitle>
                      <CardDescription>
                        Create and manage your API keys for secure access to BrandForge API
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Create New Key */}
                      <div className="flex gap-4">
                        <div className="flex-1">
                          <Label htmlFor="key-name">Key Name</Label>
                          <Input
                            id="key-name"
                            placeholder="e.g., Production App"
                            value={newKeyName}
                            onChange={(e) => setNewKeyName(e.target.value)}
                          />
                        </div>
                        <div className="flex items-end">
                          <Button>
                            <Plus className="w-4 h-4 mr-2" />
                            Create Key
                          </Button>
                        </div>
                      </div>

                      {/* Existing Keys */}
                      <div className="space-y-4">
                        {apiKeys.map((key) => (
                          <Card key={key.id} className="border-l-4 border-l-blue-600">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <h4 className="font-semibold">{key.name}</h4>
                                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-300">
                                    <span>Created: {key.created}</span>
                                    <span>Last used: {key.lastUsed}</span>
                                    <span>Usage: {key.requests.toLocaleString()}/{key.limit.toLocaleString()}</span>
                                  </div>
                                  <div className="flex items-center gap-2 mt-3">
                                    <code className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded text-sm font-mono">
                                      {showApiKey ? key.key : key.key.replace(/./g, '•').slice(0, 20) + '...'}
                                    </code>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => setShowApiKey(!showApiKey)}
                                    >
                                      {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                    </Button>
                                    <Button size="sm" variant="ghost">
                                      <Copy className="w-4 h-4" />
                                    </Button>
                                  </div>
                                </div>
                                <Button size="sm" variant="destructive">
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              {/* Documentation Tab */}
              <TabsContent value="docs" className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Book className="w-5 h-5 mr-2" />
                        API Endpoints
                      </CardTitle>
                      <CardDescription>
                        Complete reference for all available API endpoints
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {endpoints.map((endpoint, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="flex items-center gap-3 mb-2">
                              <Badge variant={endpoint.method === 'GET' ? 'secondary' : 'default'}>
                                {endpoint.method}
                              </Badge>
                              <code className="text-sm font-mono">{endpoint.path}</code>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {endpoint.description}
                            </p>
                          </div>
                        ))}
                      </div>

                      <Alert className="mt-6">
                        <Terminal className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Base URL:</strong> https://api.brandforge.ai
                          <br />
                          <strong>Authentication:</strong> Bearer token in Authorization header
                        </AlertDescription>
                      </Alert>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              {/* Code Examples Tab */}
              <TabsContent value="examples" className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Request Examples</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Tabs defaultValue="curl" className="space-y-4">
                          <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="curl">cURL</TabsTrigger>
                            <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                            <TabsTrigger value="python">Python</TabsTrigger>
                          </TabsList>
                          
                          {Object.entries(codeExamples).map(([lang, code]) => (
                            <TabsContent key={lang} value={lang}>
                              <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                                <code>{code}</code>
                              </pre>
                            </TabsContent>
                          ))}
                        </Tabs>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Response Example</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                          <code>{responseExample}</code>
                        </pre>
                      </CardContent>
                    </Card>
                  </div>
                </motion.div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-12 text-white">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Ready to Start Building?
              </h2>
              <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                Get your API key and start integrating BrandForge into your applications today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/pricing">
                  <Button size="lg" variant="secondary">
                    <Zap className="w-5 h-5 mr-2" />
                    Get API Access
                  </Button>
                </Link>
                <Link href="/bulk">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                    Try Bulk Generation
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ApiAccess;
