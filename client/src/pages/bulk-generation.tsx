import { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "wouter";
import { 
  Upload, 
  Download, 
  FileText, 
  Play,
  Pause,
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowRight,
  Sparkles,
  Zap,
  BarChart3,
  Settings
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

const BulkGeneration = () => {
  const [uploadMethod, setUploadMethod] = useState<"csv" | "manual">("csv");
  const [keywords, setKeywords] = useState("");
  const [style, setStyle] = useState("");
  const [industry, setIndustry] = useState("");
  const [quantity, setQuantity] = useState("10");

  const bulkJobs = [
    {
      id: 1,
      name: "Tech Startup Names",
      status: "completed",
      progress: 100,
      total: 500,
      processed: 500,
      created: "2024-01-20 14:30",
      completed: "2024-01-20 14:35"
    },
    {
      id: 2,
      name: "Fashion Brand Ideas",
      status: "processing",
      progress: 65,
      total: 300,
      processed: 195,
      created: "2024-01-20 15:00",
      completed: null
    },
    {
      id: 3,
      name: "Food & Beverage",
      status: "pending",
      progress: 0,
      total: 200,
      processed: 0,
      created: "2024-01-20 15:15",
      completed: null
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "processing":
        return <Play className="w-5 h-5 text-blue-500" />;
      case "pending":
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-red-100 text-red-800";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <BarChart3 className="w-4 h-4 mr-2" />
                Bulk Generation
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                Generate Hundreds of{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Brand Names
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                Upload your keywords or specify requirements to generate thousands of 
                brand names in minutes. Perfect for agencies and large projects.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Bulk Generation Interface */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <Tabs defaultValue="create" className="space-y-8">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="create">Create New Job</TabsTrigger>
                <TabsTrigger value="history">Job History</TabsTrigger>
              </TabsList>

              {/* Create New Job Tab */}
              <TabsContent value="create" className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Upload className="w-5 h-5 mr-2" />
                        Bulk Brand Name Generation
                      </CardTitle>
                      <CardDescription>
                        Generate multiple brand names using CSV upload or manual input
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Upload Method Selection */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card 
                          className={`cursor-pointer transition-all ${uploadMethod === 'csv' ? 'ring-2 ring-blue-600' : 'hover:shadow-md'}`}
                          onClick={() => setUploadMethod('csv')}
                        >
                          <CardContent className="p-6 text-center">
                            <FileText className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">CSV Upload</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              Upload a CSV file with keywords and preferences
                            </p>
                          </CardContent>
                        </Card>

                        <Card 
                          className={`cursor-pointer transition-all ${uploadMethod === 'manual' ? 'ring-2 ring-blue-600' : 'hover:shadow-md'}`}
                          onClick={() => setUploadMethod('manual')}
                        >
                          <CardContent className="p-6 text-center">
                            <Settings className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Manual Setup</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              Configure generation parameters manually
                            </p>
                          </CardContent>
                        </Card>
                      </div>

                      {/* CSV Upload */}
                      {uploadMethod === 'csv' && (
                        <div className="space-y-4">
                          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">Upload CSV File</h3>
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              Drag and drop your CSV file here, or click to browse
                            </p>
                            <Button>
                              <Upload className="w-4 h-4 mr-2" />
                              Choose File
                            </Button>
                          </div>

                          <Alert>
                            <FileText className="h-4 w-4" />
                            <AlertDescription>
                              <strong>CSV Format:</strong> Include columns for keywords, style, industry, and quantity.
                              <br />
                              <a href="#" className="text-blue-600 hover:underline">Download sample CSV template</a>
                            </AlertDescription>
                          </Alert>
                        </div>
                      )}

                      {/* Manual Setup */}
                      {uploadMethod === 'manual' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="keywords">Keywords (one per line)</Label>
                              <Textarea
                                id="keywords"
                                placeholder="tech&#10;innovation&#10;digital&#10;smart"
                                value={keywords}
                                onChange={(e) => setKeywords(e.target.value)}
                                rows={6}
                              />
                            </div>

                            <div>
                              <Label htmlFor="quantity">Names per keyword</Label>
                              <Select value={quantity} onValueChange={setQuantity}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="5">5 names</SelectItem>
                                  <SelectItem value="10">10 names</SelectItem>
                                  <SelectItem value="25">25 names</SelectItem>
                                  <SelectItem value="50">50 names</SelectItem>
                                  <SelectItem value="100">100 names</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="style">Brand Style</Label>
                              <Select value={style} onValueChange={setStyle}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select style" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="modern">Modern</SelectItem>
                                  <SelectItem value="classic">Classic</SelectItem>
                                  <SelectItem value="playful">Playful</SelectItem>
                                  <SelectItem value="professional">Professional</SelectItem>
                                  <SelectItem value="luxury">Luxury</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label htmlFor="industry">Industry</Label>
                              <Select value={industry} onValueChange={setIndustry}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select industry" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="technology">Technology</SelectItem>
                                  <SelectItem value="healthcare">Healthcare</SelectItem>
                                  <SelectItem value="fashion">Fashion</SelectItem>
                                  <SelectItem value="food">Food & Beverage</SelectItem>
                                  <SelectItem value="finance">Finance</SelectItem>
                                  <SelectItem value="education">Education</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="pt-4">
                              <Alert>
                                <BarChart3 className="h-4 w-4" />
                                <AlertDescription>
                                  <strong>Estimated generation:</strong> {keywords.split('\n').filter(k => k.trim()).length * parseInt(quantity)} brand names
                                </AlertDescription>
                              </Alert>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex gap-4 pt-6 border-t">
                        <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                          <Play className="w-4 h-4 mr-2" />
                          Start Generation
                        </Button>
                        <Button variant="outline">
                          Save as Draft
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              {/* Job History Tab */}
              <TabsContent value="history" className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Clock className="w-5 h-5 mr-2" />
                        Generation History
                      </CardTitle>
                      <CardDescription>
                        Track and manage your bulk generation jobs
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {bulkJobs.map((job) => (
                          <Card key={job.id} className="border-l-4 border-l-blue-600">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center gap-3">
                                  {getStatusIcon(job.status)}
                                  <div>
                                    <h4 className="font-semibold">{job.name}</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-300">
                                      Created: {job.created}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge className={getStatusColor(job.status)}>
                                    {job.status}
                                  </Badge>
                                  {job.status === "completed" && (
                                    <Button size="sm" variant="outline">
                                      <Download className="w-4 h-4 mr-2" />
                                      Download
                                    </Button>
                                  )}
                                </div>
                              </div>

                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span>Progress: {job.processed}/{job.total}</span>
                                  <span>{job.progress}%</span>
                                </div>
                                <Progress value={job.progress} className="h-2" />
                              </div>

                              {job.completed && (
                                <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                                  Completed: {job.completed}
                                </p>
                              )}
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Bulk Generation Features
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardContent className="p-6">
                <Upload className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">CSV Upload</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Upload thousands of keywords at once with custom parameters
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <BarChart3 className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Progress Tracking</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Real-time progress updates and job status monitoring
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <Download className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Export Results</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Download results in multiple formats (CSV, PDF, JSON)
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-12 text-white">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Need Bulk Generation Access?
              </h2>
              <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                Upgrade to Pro or Enterprise to unlock bulk generation capabilities.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/pricing">
                  <Button size="lg" variant="secondary">
                    <Zap className="w-5 h-5 mr-2" />
                    View Pricing
                  </Button>
                </Link>
                <Link href="/api">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                    API Access
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default BulkGeneration;
