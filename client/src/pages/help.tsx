import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { 
  Search, 
  Book, 
  ChevronRight,
  ChevronDown,
  Star,
  ArrowRight,
  Sparkles,
  MessageCircle,
  Code,
  CreditCard,
  Settings,
  HelpCircle,
  ExternalLink
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

const Help = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [openFaq, setOpenFaq] = useState<string | null>(null);

  const helpCategories = [
    {
      icon: Book,
      title: "Getting Started",
      description: "Learn the basics of BrandForge",
      articles: 12,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      icon: Code,
      title: "API Documentation",
      description: "Integrate BrandForge into your apps",
      articles: 8,
      color: "text-purple-600",
      bgColor: "bg-purple-100"
    },
    {
      icon: CreditCard,
      title: "Billing & Plans",
      description: "Manage your subscription",
      articles: 6,
      color: "text-green-600",
      bgColor: "bg-green-100"
    },
    {
      icon: Settings,
      title: "Account Settings",
      description: "Customize your experience",
      articles: 5,
      color: "text-orange-600",
      bgColor: "bg-orange-100"
    }
  ];

  const popularArticles = [
    {
      title: "How to generate your first brand names",
      category: "Getting Started",
      views: 1250,
      rating: 4.8
    },
    {
      title: "Understanding brand name styles",
      category: "Getting Started", 
      views: 980,
      rating: 4.9
    },
    {
      title: "API authentication and rate limits",
      category: "API Documentation",
      views: 750,
      rating: 4.7
    },
    {
      title: "Upgrading to Pro plan",
      category: "Billing & Plans",
      views: 650,
      rating: 4.6
    },
    {
      title: "Bulk generation best practices",
      category: "API Documentation",
      views: 520,
      rating: 4.8
    }
  ];

  const faqs = [
    {
      id: "1",
      question: "How does the AI brand name generation work?",
      answer: "BrandForge uses advanced GPT-4 technology to analyze your keywords, industry context, and style preferences. Our AI considers linguistic patterns, cultural relevance, and trademark-friendly combinations to generate unique, memorable brand names that align with your vision."
    },
    {
      id: "2", 
      question: "Can I use the generated names commercially?",
      answer: "Yes, all names generated by BrandForge are available for commercial use. However, we recommend conducting proper trademark searches and domain availability checks before finalizing your brand name. We provide basic screening tools, but professional legal advice is recommended for important decisions."
    },
    {
      id: "3",
      question: "What's the difference between Free and Pro plans?",
      answer: "Free plans include 10 generations per month with basic features. Pro plans offer unlimited generations, API access, bulk generation, advanced export options, priority support, and additional features like trademark screening and analytics dashboard."
    },
    {
      id: "4",
      question: "How accurate is the domain availability checker?",
      answer: "Our domain checker provides real-time availability for most TLDs. However, domain status can change rapidly, so we recommend double-checking with your preferred registrar before making final decisions. Pro users get access to premium domain suggestions and bulk domain checking."
    },
    {
      id: "5",
      question: "Can I integrate BrandForge into my own application?",
      answer: "Yes! Pro and Enterprise plans include API access. Our REST API allows you to integrate brand name generation directly into your applications, websites, or workflows. We provide comprehensive documentation and code examples for popular programming languages."
    },
    {
      id: "6",
      question: "What industries does BrandForge support?",
      answer: "BrandForge supports 50+ industries including Technology, Healthcare, Fashion, Food & Beverage, Finance, Education, and more. Our AI is trained on industry-specific terminology and naming conventions to provide relevant suggestions for your sector."
    }
  ];

  const quickLinks = [
    { title: "API Reference", href: "/api", icon: Code },
    { title: "Pricing Plans", href: "/pricing", icon: CreditCard },
    { title: "Contact Support", href: "/support", icon: MessageCircle },
    { title: "Feature Requests", href: "/support", icon: Star }
  ];

  const filteredArticles = popularArticles.filter(article =>
    article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    article.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <Book className="w-4 h-4 mr-2" />
                Help Center
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                How Can We{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Help You?
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                Find answers to your questions, learn how to use BrandForge effectively, 
                and discover tips for creating perfect brand names.
              </p>

              {/* Search Bar */}
              <div className="relative max-w-2xl mx-auto">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search for help articles, guides, and FAQs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 py-4 text-lg"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Help Categories */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Browse by Category
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {helpCategories.map((category, index) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300 cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <div className={`w-16 h-16 rounded-full ${category.bgColor} flex items-center justify-center mx-auto mb-4`}>
                      <category.icon className={`w-8 h-8 ${category.color}`} />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">{category.title}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      {category.description}
                    </p>
                    <Badge variant="secondary">
                      {category.articles} articles
                    </Badge>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Articles */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Popular Articles
              </h2>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Articles List */}
              <div className="lg:col-span-2 space-y-4">
                {filteredArticles.map((article, index) => (
                  <motion.div
                    key={article.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card className="hover:shadow-md transition-shadow duration-300 cursor-pointer">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold mb-2 hover:text-blue-600 transition-colors">
                              {article.title}
                            </h3>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                              <Badge variant="outline">{article.category}</Badge>
                              <span>{article.views.toLocaleString()} views</span>
                              <div className="flex items-center">
                                <Star className="w-4 h-4 text-yellow-500 mr-1" />
                                <span>{article.rating}</span>
                              </div>
                            </div>
                          </div>
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Quick Links Sidebar */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Links</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {quickLinks.map((link) => (
                      <Link key={link.title} href={link.href}>
                        <Button variant="ghost" className="w-full justify-start">
                          <link.icon className="w-4 h-4 mr-2" />
                          {link.title}
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </Button>
                      </Link>
                    ))}
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6 text-center">
                    <MessageCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">Still Need Help?</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                      Can't find what you're looking for? Our support team is here to help.
                    </p>
                    <Link href="/support">
                      <Button className="w-full">
                        Contact Support
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Frequently Asked Questions
            </h2>
          </motion.div>

          <div className="max-w-4xl mx-auto space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Collapsible
                  open={openFaq === faq.id}
                  onOpenChange={() => setOpenFaq(openFaq === faq.id ? null : faq.id)}
                >
                  <Card>
                    <CollapsibleTrigger asChild>
                      <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg text-left">{faq.question}</CardTitle>
                          {openFaq === faq.id ? (
                            <ChevronDown className="w-5 h-5 text-gray-400" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      </CardHeader>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <CardContent className="pt-0">
                        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                      </CardContent>
                    </CollapsibleContent>
                  </Card>
                </Collapsible>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Help;
