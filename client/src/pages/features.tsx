import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { 
  <PERSON>, 
  Palette, 
  Download, 
  Star, 
  ArrowRight,
  Sparkles,
  Target,
  Lightbulb,
  Rocket,
  Search,
  Globe,
  Shield,
  Zap,
  BarChart3,
  Users,
  Clock,
  CheckCircle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

const Features = () => {
  const coreFeatures = [
    {
      icon: Brain,
      title: "AI Brand Name Engine",
      description: "Advanced GPT-4 powered algorithm that understands context, industry trends, and linguistic patterns to generate unique, memorable brand names.",
      features: ["Context-aware generation", "Industry-specific terminology", "Linguistic pattern analysis", "Trademark-friendly suggestions"]
    },
    {
      icon: Palette,
      title: "Style Selector",
      description: "Choose from multiple brand personality styles to match your vision perfectly.",
      features: ["Modern & Tech-savvy", "Classic & Professional", "Playful & Creative", "Luxury & Premium"]
    },
    {
      icon: Target,
      title: "Industry Targeting",
      description: "Specialized algorithms for 50+ industries ensure relevant and appropriate naming suggestions.",
      features: ["Technology & SaaS", "Healthcare & Wellness", "Fashion & Lifestyle", "Food & Beverage"]
    },
    {
      icon: Star,
      title: "Favorites System",
      description: "Save, organize, and manage your favorite brand names with powerful collection tools.",
      features: ["Unlimited favorites", "Custom collections", "Notes & ratings", "Share with team"]
    },
    {
      icon: Download,
      title: "Export & Save",
      description: "Professional export options for presentations, trademark searches, and team collaboration.",
      features: ["PDF reports", "CSV exports", "Brand packages", "Logo mockups"]
    },
    {
      icon: Search,
      title: "Domain Availability",
      description: "Real-time domain availability checking for .com, .net, .org, and 100+ TLDs.",
      features: ["Instant domain checks", "Alternative suggestions", "Premium domain options", "Bulk domain search"]
    }
  ];

  const advancedFeatures = [
    {
      icon: Globe,
      title: "Multi-language Support",
      description: "Generate brand names in multiple languages and check cultural appropriateness.",
      badge: "Pro"
    },
    {
      icon: Shield,
      title: "Trademark Screening",
      description: "Basic trademark conflict detection to help avoid legal issues.",
      badge: "Pro"
    },
    {
      icon: BarChart3,
      title: "Analytics Dashboard",
      description: "Track your naming projects, view generation history, and analyze trends.",
      badge: "Pro"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Share projects with team members and collaborate on naming decisions.",
      badge: "Enterprise"
    },
    {
      icon: Rocket,
      title: "API Access",
      description: "Integrate BrandForge into your applications with our powerful REST API.",
      badge: "Pro"
    },
    {
      icon: Clock,
      title: "Bulk Generation",
      description: "Generate hundreds of names at once with CSV upload and batch processing.",
      badge: "Pro"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <Sparkles className="w-4 h-4 mr-2" />
                Powerful Features
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                Everything You Need for{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Perfect Branding
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                Discover all the powerful features that make BrandForge the ultimate 
                AI-powered brand naming solution for businesses of all sizes.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Core Features
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Essential tools for creating memorable brand names
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 flex items-center justify-center mb-4">
                      <feature.icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {feature.features.map((item, i) => (
                        <li key={i} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                          {item}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Advanced Features */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Advanced Features
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Professional tools for serious brand builders
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {advancedFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300 relative">
                  <Badge className="absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                    {feature.badge}
                  </Badge>
                  <CardHeader className="pr-16">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 flex items-center justify-center mb-4">
                      <feature.icon className="w-6 h-6 text-purple-600" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-12 text-white">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Ready to Experience These Features?
              </h2>
              <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                Start generating perfect brand names today with our powerful AI technology.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/">
                  <Button size="lg" variant="secondary">
                    <Zap className="w-5 h-5 mr-2" />
                    Try Free Now
                  </Button>
                </Link>
                <Link href="/pricing">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                    View Pricing
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Features;
