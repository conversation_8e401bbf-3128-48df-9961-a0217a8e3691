import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Heart, Brain, Rocket, Palette } from "lucide-react";
import { BrandNameCard } from "@/components/brand-name-card";
import { SavedNamesModal } from "@/components/saved-names-modal";
import { LoadingSkeleton } from "@/components/loading-skeleton";
import { useFavorites } from "@/hooks/use-favorites";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { GenerateNamesResponse } from "@/lib/types";

export default function Home() {
  const [keyword, setKeyword] = useState("");
  const [selectedStyle, setSelectedStyle] = useState("");
  const [showStyleSelection, setShowStyleSelection] = useState(false);
  const [lastGeneratedKeyword, setLastGeneratedKeyword] = useState("");
  const [showSavedModal, setShowSavedModal] = useState(false);
  const { toast } = useToast();
  
  const {
    savedNames,
    saveName,
    removeName,
    isNameSaved,
    clearAll,
    exportCSV,
    count: savedCount
  } = useFavorites();

  const generateNamesMutation = useMutation({
    mutationFn: async ({ keyword, style }: { keyword: string; style?: string }): Promise<GenerateNamesResponse> => {
      const response = await apiRequest("POST", "/api/generate-names", { keyword, style });
      return response.json();
    },
    onSuccess: (data) => {
      setLastGeneratedKeyword(data.keyword);
      setShowStyleSelection(false);
      toast({
        title: "Success!",
        description: `Generated ${data.count} brand names for "${data.keyword}"`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleGenerate = () => {
    if (!keyword.trim()) {
      toast({
        title: "Error",
        description: "Please enter a keyword or niche",
        variant: "destructive",
      });
      return;
    }
    setShowStyleSelection(true);
  };

  const handleStyleSelect = (style: string) => {
    setSelectedStyle(style);
    generateNamesMutation.mutate({ keyword: keyword.trim(), style });
  };

  const brandNameStyles = [
    {
      id: 'short-catchy',
      title: 'Short & Catchy',
      description: '1-word, 4-7 letters',
      examples: 'Flux, Peak, Vibe'
    },
    {
      id: 'real-words',
      title: 'Real Words',
      description: 'Existing English words, creative usage',
      examples: 'Horizon, Quest, Summit'
    },
    {
      id: 'compound',
      title: 'Compound Words',
      description: 'Two words combined',
      examples: 'PowerCore, NextWave, FlexFlow'
    },
    {
      id: 'invented',
      title: 'Made-up / Invented',
      description: 'Unique invented names',
      examples: 'Zephyra, Nexova, Lumira'
    },
    {
      id: 'trendy-startup',
      title: 'Trendy Startup-style',
      description: 'Names ending in -ly, -io, -ify',
      examples: 'Buildly, Streamio, Growify'
    },
    {
      id: 'luxury-premium',
      title: 'Luxury / Premium Style',
      description: 'Sophisticated, high-end feel',
      examples: 'Aurelia, Meridian, Valence'
    }
  ];

  const handleToggleFavorite = (brandName: any) => {
    if (isNameSaved(brandName.name)) {
      removeName(brandName.name);
      toast({
        title: "Removed from favorites",
        description: `${brandName.name} has been removed from your saved names`,
      });
    } else {
      saveName(brandName);
      toast({
        title: "Added to favorites",
        description: `${brandName.name} has been saved to your favorites`,
      });
    }
  };

  const handleClearAll = () => {
    if (window.confirm("Are you sure you want to clear all saved names?")) {
      clearAll();
      toast({
        title: "Cleared",
        description: "All saved names have been removed",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                <span className="text-primary">Brand</span>Forge
              </h1>
            </div>
            <nav className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <a href="#" className="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors">How it Works</a>
                <a href="#" className="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors">Pricing</a>
                <a href="#" className="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors">Blog</a>
                <Button 
                  onClick={() => setShowSavedModal(true)}
                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                >
                  <Heart className="mr-2 fill-current" size={16} />
                  Saved Names ({savedCount})
                </Button>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-50 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Generate the Perfect{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-purple-600">
                Brand Name
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              AI-powered brand name generator that creates unique, memorable, and brandable names for your startup or product in seconds.
            </p>
            
            {/* Search Form */}
            <div className="max-w-xl mx-auto">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="text-gray-400" size={20} />
                </div>
                <Input
                  type="text"
                  value={keyword}
                  onChange={(e) => setKeyword(e.target.value)}
                  placeholder="Enter your keyword or niche (e.g., tech, fitness, food)"
                  className="w-full pl-12 pr-32 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent"
                  onKeyPress={(e) => e.key === 'Enter' && handleGenerate()}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                  <Button 
                    onClick={handleGenerate}
                    disabled={generateNamesMutation.isPending}
                    className="bg-primary text-primary-foreground px-6 py-2 rounded-lg font-semibold hover:bg-primary/90 transform hover:scale-105 transition-all duration-200"
                  >
                    {generateNamesMutation.isPending ? "Generating..." : "Generate"}
                  </Button>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-3">
                ✨ Powered by AI • 🚀 Instant Results • 💎 Premium Quality Names
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Style Selection Modal */}
      {showStyleSelection && (
        <section className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Brand Name Style</h2>
            <p className="text-gray-600">
              Select the style that best fits your vision for "{keyword}"
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {brandNameStyles.map((style) => (
              <Card 
                key={style.id} 
                className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-primary"
                onClick={() => handleStyleSelect(style.id)}
              >
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{style.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{style.description}</p>
                  <div className="text-xs text-gray-500">
                    <span className="font-medium">Examples:</span> {style.examples}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <Button 
              variant="outline"
              onClick={() => setShowStyleSelection(false)}
              className="mr-4"
            >
              Back to Search
            </Button>
          </div>
        </section>
      )}

      {/* Loading State */}
      {generateNamesMutation.isPending && <LoadingSkeleton />}

      {/* Results Section */}
      {generateNamesMutation.data && !generateNamesMutation.isPending && (
        <section className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Your Brand Name Suggestions</h2>
            <p className="text-gray-600">
              Here are {generateNamesMutation.data.count} unique brand names generated for "{lastGeneratedKeyword}"
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {generateNamesMutation.data.names.map((brandName) => (
              <BrandNameCard
                key={brandName.id}
                brandName={brandName}
                isSaved={isNameSaved(brandName.name)}
                onToggleFavorite={handleToggleFavorite}
              />
            ))}
          </div>

          <div className="text-center mt-12">
            <Button 
              onClick={() => generateNamesMutation.mutate({ keyword: lastGeneratedKeyword, style: selectedStyle })}
              disabled={generateNamesMutation.isPending}
              className="bg-gradient-to-r from-primary to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-primary/90 hover:to-purple-700 transform hover:scale-105 transition-all duration-200"
            >
              <Brain className="mr-2" size={20} />
              Generate More Names
            </Button>
          </div>
        </section>
      )}

      {/* Features Section */}
      <section className="bg-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose BrandForge?</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our AI-powered platform combines creativity with market intelligence to deliver brand names that resonate with your audience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <Brain className="text-primary" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">AI-Powered Generation</h3>
              <p className="text-gray-600">Advanced GPT-4 algorithms analyze trends, linguistics, and brandability to create unique names.</p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <Rocket className="text-green-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Instant Domain Check</h3>
              <p className="text-gray-600">Real-time domain availability checking with direct links to secure your perfect domain.</p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <Palette className="text-purple-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Logo Generation</h3>
              <p className="text-gray-600">Seamless integration with professional logo design tools to complete your brand identity.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">
                <span className="text-blue-400">Brand</span>Forge
              </h3>
              <p className="text-gray-400 mb-6 max-w-md">
                The most advanced AI-powered brand name generator trusted by thousands of entrepreneurs and businesses worldwide.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">How it Works</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API Access</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Bulk Generation</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 BrandForge. All rights reserved. Made with ❤️ for entrepreneurs.</p>
          </div>
        </div>
      </footer>

      {/* Saved Names Modal */}
      <SavedNamesModal
        isOpen={showSavedModal}
        onClose={() => setShowSavedModal(false)}
        savedNames={savedNames}
        onRemove={removeName}
        onClearAll={handleClearAll}
        onExportCSV={exportCSV}
      />
    </div>
  );
}
