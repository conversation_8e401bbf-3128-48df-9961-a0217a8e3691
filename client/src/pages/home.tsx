import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>, <PERSON>, Palette, Menu } from "lucide-react";
import { PremiumHero } from "@/components/premium-hero";
import { BrandTypeSelector } from "@/components/brand-type-selector";
import { BrandNameCard } from "@/components/brand-name-card";
import { FavoritesSidebar } from "@/components/favorites-sidebar";
import { LoadingSkeleton } from "@/components/loading-skeleton";
import { useFavorites } from "@/hooks/use-favorites";
import { useToast } from "@/hooks/use-toast";
import { useScrollReveal } from "@/hooks/use-scroll-reveal";
import { apiRequest } from "@/lib/queryClient";
import type { GenerateNamesResponse } from "@/lib/types";

export default function Home() {
  const [keyword, setKeyword] = useState("");
  const [selectedStyle, setSelectedStyle] = useState("");
  const [showStyleSelection, setShowStyleSelection] = useState(false);
  const [lastGeneratedKeyword, setLastGeneratedKeyword] = useState("");
  const [showFavoritesSidebar, setShowFavoritesSidebar] = useState(false);
  const [currentStep, setCurrentStep] = useState<'hero' | 'style' | 'results'>('hero');
  const { toast } = useToast();
  const { ref: featuresRef, isVisible: featuresVisible } = useScrollReveal();
  
  const {
    savedNames,
    saveName,
    removeName,
    isNameSaved,
    clearAll,
    exportCSV,
    count: savedCount
  } = useFavorites();

  const generateNamesMutation = useMutation({
    mutationFn: async ({ keyword, style }: { keyword: string; style?: string }): Promise<GenerateNamesResponse> => {
      const response = await apiRequest("POST", "/api/generate-names", { keyword, style });
      return response.json();
    },
    onSuccess: (data) => {
      setLastGeneratedKeyword(data.keyword);
      setShowStyleSelection(false);
      toast({
        title: "Success!",
        description: `Generated ${data.count} brand names for "${data.keyword}"`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleGenerate = () => {
    if (!keyword.trim()) {
      toast({
        title: "Error",
        description: "Please enter a keyword or niche",
        variant: "destructive",
      });
      return;
    }
    setCurrentStep('style');
    setShowStyleSelection(true);
  };

  const handleStyleSelect = (style: string) => {
    setSelectedStyle(style);
    setCurrentStep('results');
    generateNamesMutation.mutate({ keyword: keyword.trim(), style });
  };



  const handleToggleFavorite = (brandName: any) => {
    if (isNameSaved(brandName.name)) {
      removeName(brandName.name);
      toast({
        title: "Removed from favorites",
        description: `${brandName.name} has been removed from your saved names`,
      });
    } else {
      saveName(brandName);
      toast({
        title: "Added to favorites",
        description: `${brandName.name} has been saved to your favorites`,
      });
    }
  };

  const handleClearAll = () => {
    if (window.confirm("Are you sure you want to clear all saved names?")) {
      clearAll();
      toast({
        title: "Cleared",
        description: "All saved names have been removed",
      });
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Premium Header */}
      <motion.header
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="fixed top-0 left-0 right-0 z-30 bg-white/80 backdrop-blur-md border-b border-gray-200/50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <motion.div
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <h1 className="text-2xl font-bold text-gray-900">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-accent-purple">Brand</span>Forge
              </h1>
            </motion.div>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors">Features</a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors">Pricing</a>
              <a href="#about" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors">About</a>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  onClick={() => setShowFavoritesSidebar(true)}
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <Heart className="mr-2 fill-current" size={16} />
                  Favorites ({savedCount})
                </Button>
              </motion.div>
            </nav>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFavoritesSidebar(true)}
              className="md:hidden"
            >
              <Menu size={20} />
            </Button>
          </div>
        </div>
      </motion.header>

      {/* Premium Hero Section */}
      <AnimatePresence mode="wait">
        {currentStep === 'hero' && (
          <PremiumHero
            keyword={keyword}
            onKeywordChange={setKeyword}
            onGenerate={handleGenerate}
            isGenerating={generateNamesMutation.isPending}
          />
        )}
      </AnimatePresence>

      {/* Brand Type Selection */}
      <AnimatePresence mode="wait">
        {currentStep === 'style' && (
          <motion.section
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6 }}
            className="min-h-screen flex items-center justify-center py-20 px-4"
          >
            <BrandTypeSelector
              onStyleSelect={handleStyleSelect}
              selectedStyle={selectedStyle}
              isGenerating={generateNamesMutation.isPending}
            />
          </motion.section>
        )}
      </AnimatePresence>

      {/* Loading State */}
      <AnimatePresence>
        {generateNamesMutation.isPending && <LoadingSkeleton />}
      </AnimatePresence>

      {/* Results Section */}
      <AnimatePresence mode="wait">
        {generateNamesMutation.data && !generateNamesMutation.isPending && currentStep === 'results' && (
          <motion.section
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6 }}
            className="min-h-screen py-20 px-4"
          >
            <div className="max-w-6xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center mb-12"
              >
                <h2 className="text-4xl font-bold text-gray-900 mb-4">
                  Your Brand Name Suggestions
                </h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  Here are {generateNamesMutation.data.count} unique brand names generated for "{lastGeneratedKeyword}"
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                {generateNamesMutation.data.names.map((brandName, index) => (
                  <BrandNameCard
                    key={brandName.id}
                    brandName={brandName}
                    isSaved={isNameSaved(brandName.name)}
                    onToggleFavorite={handleToggleFavorite}
                    index={index}
                  />
                ))}
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="text-center space-y-4"
              >
                <Button
                  onClick={() => generateNamesMutation.mutate({ keyword: lastGeneratedKeyword, style: selectedStyle })}
                  disabled={generateNamesMutation.isPending}
                  className="btn-premium px-8 py-3 text-lg"
                >
                  <Brain className="mr-2" size={20} />
                  Generate More Names
                </Button>
                <div className="flex justify-center gap-4">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep('style')}
                    className="px-6 py-2"
                  >
                    Try Different Style
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep('hero')}
                    className="px-6 py-2"
                  >
                    New Search
                  </Button>
                </div>
              </motion.div>
            </div>
          </motion.section>
        )}
      </AnimatePresence>

      {/* Features Section */}
      <motion.section
        ref={featuresRef}
        id="features"
        className="bg-gradient-to-br from-gray-50 to-white py-20"
      >
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={featuresVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose BrandForge?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our AI-powered platform combines creativity with market intelligence to deliver brand names that resonate with your audience.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Brain,
                title: "AI-Powered Generation",
                description: "Advanced GPT-4 algorithms analyze trends, linguistics, and brandability to create unique names.",
                color: "from-blue-500 to-cyan-500",
                delay: 0.2
              },
              {
                icon: Rocket,
                title: "Instant Domain Check",
                description: "Real-time domain availability checking with direct links to secure your perfect domain.",
                color: "from-green-500 to-emerald-500",
                delay: 0.4
              },
              {
                icon: Palette,
                title: "Logo Generation",
                description: "Seamless integration with professional logo design tools to complete your brand identity.",
                color: "from-purple-500 to-pink-500",
                delay: 0.6
              }
            ].map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={featuresVisible ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: feature.delay }}
                  className="text-center group"
                >
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    className={`bg-gradient-to-r ${feature.color} w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                  >
                    <Icon className="text-white" size={32} />
                  </motion.div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </motion.section>

      {/* Why a branded name? Section */}
      <motion.section
        className="relative py-20 overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-float" />
          <div className="absolute top-1/2 right-20 w-24 h-24 bg-white/10 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }} />
          <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-white/10 rounded-full blur-xl animate-float" style={{ animationDelay: '4s' }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-white"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                Why a branded name?
              </h2>
              <div className="space-y-6 text-lg leading-relaxed">
                <p className="text-white/90">
                  For new businesses, naming options can seem quite limited. Short domains are very expensive, yet longer multi-word names don't inspire confidence.
                </p>
                <p className="text-white/90">
                  In 2024, many startups are choosing a short, branded name - a name that's unique, memorable and affordable.
                </p>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 border border-white/30"
                >
                  <h3 className="text-xl font-semibold mb-3">Benefits of Branded Names:</h3>
                  <ul className="space-y-2 text-white/90">
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-white rounded-full" />
                      Unique and memorable
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-white rounded-full" />
                      Affordable domain options
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-white rounded-full" />
                      Professional appearance
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-white rounded-full" />
                      Easy to trademark
                    </li>
                  </ul>
                </motion.div>
              </div>
            </motion.div>

            {/* Right side - Floating Brand Logos */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative h-96 lg:h-[500px]"
            >
              {/* Central Google Logo - Perfectly Centered */}
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 1, delay: 0.5 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  top: '45%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -12, 0],
                    rotate: [0, 1, -1, 0]
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="w-36 h-36 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-3xl flex items-center justify-center border-4 border-white/70 backdrop-blur-sm relative overflow-hidden"
                  whileHover={{ scale: 1.1 }}
                >
                  {/* Premium glassmorphism layers */}
                  <div className="absolute inset-0 bg-white/30 rounded-full"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent rounded-full"></div>

                  {/* Logo placeholder */}
                  <div className="relative z-10 w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center border-3 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
                    <span className="text-sm text-gray-500 font-medium">Google</span>
                  </div>

                  {/* Floating particles around center */}
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-3 -right-3 w-5 h-5 bg-blue-400/20 rounded-full"
                  />
                  <motion.div
                    animate={{ rotate: -360 }}
                    transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                    className="absolute -bottom-2 -left-2 w-4 h-4 bg-red-400/20 rounded-full"
                  />
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="absolute top-2 -left-3 w-3 h-3 bg-yellow-400/20 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Beautiful Brand Logo Circles - Premium Design */}

              {/* Spotify - Top Center */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, y: -50 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  top: '8%',
                  left: '50%',
                  transform: 'translateX(-50%)'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -12, 0],
                    rotate: [0, 2, -2, 0]
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="group relative"
                >
                  <div className="w-24 h-24 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-2xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-3xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    {/* Glassmorphism effect */}
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    {/* Logo placeholder */}
                    <div className="relative z-10 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Spotify</span>
                    </div>
                  </div>

                  {/* Floating particles */}
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-2 -right-2 w-4 h-4 bg-green-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Trulia - Top Left */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, x: -50 }}
                whileInView={{ opacity: 1, scale: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  top: '20%',
                  left: '12%'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, -1, 1, 0]
                  }}
                  transition={{
                    duration: 5.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5
                  }}
                  className="group relative"
                >
                  <div className="w-20 h-20 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Trulia</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: -360 }}
                    transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-1 -left-1 w-3 h-3 bg-green-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Lyft - Top Right */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, x: 50 }}
                whileInView={{ opacity: 1, scale: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  top: '20%',
                  right: '12%'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -8, 0],
                    rotate: [0, 1.5, -1.5, 0]
                  }}
                  transition={{
                    duration: 4.8,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                  className="group relative"
                >
                  <div className="w-20 h-20 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Lyft</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 18, repeat: Infinity, ease: "linear" }}
                    className="absolute -bottom-1 -right-1 w-3 h-3 bg-pink-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Vimeo - Left */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, x: -50 }}
                whileInView={{ opacity: 1, scale: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.4, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  top: '50%',
                  left: '5%',
                  transform: 'translateY(-50%)'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, -2, 2, 0]
                  }}
                  transition={{
                    duration: 5.2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1.5
                  }}
                  className="group relative"
                >
                  <div className="w-20 h-20 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Vimeo</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 22, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-1 -right-1 w-3 h-3 bg-blue-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Strikingly - Right (Larger Premium Circle) */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, x: 50 }}
                whileInView={{ opacity: 1, scale: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.5, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  top: '45%',
                  right: '5%',
                  transform: 'translateY(-50%)'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -15, 0],
                    rotate: [0, 1, -1, 0]
                  }}
                  transition={{
                    duration: 6.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 2
                  }}
                  className="group relative"
                >
                  <div className="w-28 h-28 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-2xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-3xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-18 h-18 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Strikingly</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: -360 }}
                    transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-2 -right-2 w-4 h-4 bg-purple-400/30 rounded-full"
                  />
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                    className="absolute -bottom-1 -left-1 w-3 h-3 bg-gray-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Instagram - Bottom Left */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, y: 50 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  bottom: '20%',
                  left: '18%'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -12, 0],
                    rotate: [0, -1.5, 1.5, 0]
                  }}
                  transition={{
                    duration: 4.8,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 2.5
                  }}
                  className="group relative"
                >
                  <div className="w-22 h-22 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-14 h-14 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Instagram</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 16, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-400/30 to-pink-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Zynga - Bottom Right */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, y: 50 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  bottom: '20%',
                  right: '18%'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, 2, -2, 0]
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 3
                  }}
                  className="group relative"
                >
                  <div className="w-22 h-22 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-14 h-14 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Zynga</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: -360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="absolute -bottom-1 -left-1 w-3 h-3 bg-red-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Rovio - Bottom Center Left */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, y: 50 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  bottom: '8%',
                  left: '28%'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -8, 0],
                    rotate: [0, -1, 1, 0]
                  }}
                  transition={{
                    duration: 4.3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 3.5
                  }}
                  className="group relative"
                >
                  <div className="w-20 h-20 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Rovio</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 24, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-1 -left-1 w-3 h-3 bg-red-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Airbnb - Bottom Center Right */}
              <motion.div
                initial={{ opacity: 0, scale: 0.3, y: 50 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.9, type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute"
                style={{
                  bottom: '8%',
                  right: '28%'
                }}
              >
                <motion.div
                  animate={{
                    y: [0, -14, 0],
                    rotate: [0, 1.5, -1.5, 0]
                  }}
                  transition={{
                    duration: 5.2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 4
                  }}
                  className="group relative"
                >
                  <div className="w-24 h-24 bg-gradient-to-br from-white to-gray-50 rounded-full shadow-2xl flex items-center justify-center border-2 border-white/60 backdrop-blur-sm hover:shadow-3xl transition-all duration-500 cursor-pointer relative overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>

                    <div className="relative z-10 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-gray-400 transition-colors">
                      <span className="text-xs text-gray-500 font-medium">Airbnb</span>
                    </div>
                  </div>

                  <motion.div
                    animate={{ rotate: -360 }}
                    transition={{ duration: 28, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-2 -right-2 w-4 h-4 bg-coral-400/30 rounded-full"
                  />
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 14, repeat: Infinity, ease: "linear" }}
                    className="absolute -bottom-1 -left-1 w-3 h-3 bg-red-400/30 rounded-full"
                  />
                </motion.div>
              </motion.div>

              {/* Central highlight circle */}
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 1, delay: 0.5 }}
                viewport={{ once: true }}
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/20 rounded-full border-2 border-white/40 flex items-center justify-center backdrop-blur-sm"
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="text-white text-center"
                >
                  <div className="text-2xl font-bold">✨</div>
                  <div className="text-xs font-medium">Branded</div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Premium Footer */}
      <footer className="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <motion.h3
                className="text-3xl font-bold mb-4"
                whileHover={{ scale: 1.05 }}
              >
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-400 to-accent-purple">Brand</span>Forge
              </motion.h3>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                The most advanced AI-powered brand name generator trusted by thousands of entrepreneurs and businesses worldwide.
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-white mb-4">Product</h4>
              <ul className="space-y-3 text-gray-300">
                <li><a href="#features" className="hover:text-primary-400 transition-colors">Features</a></li>
                <li><a href="#pricing" className="hover:text-primary-400 transition-colors">Pricing</a></li>
                <li><a href="#api" className="hover:text-primary-400 transition-colors">API Access</a></li>
                <li><a href="#bulk" className="hover:text-primary-400 transition-colors">Bulk Generation</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-white mb-4">Support</h4>
              <ul className="space-y-3 text-gray-300">
                <li><a href="#help" className="hover:text-primary-400 transition-colors">Help Center</a></li>
                <li><a href="#contact" className="hover:text-primary-400 transition-colors">Contact Us</a></li>
                <li><a href="#privacy" className="hover:text-primary-400 transition-colors">Privacy Policy</a></li>
                <li><a href="#terms" className="hover:text-primary-400 transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 BrandForge. All rights reserved. Made with ❤️ for entrepreneurs.</p>
          </div>
        </div>
      </footer>

      {/* Favorites Sidebar */}
      <FavoritesSidebar
        isOpen={showFavoritesSidebar}
        onClose={() => setShowFavoritesSidebar(false)}
        savedNames={savedNames}
        onRemove={removeName}
        onClearAll={handleClearAll}
        onExportCSV={exportCSV}
      />
    </div>
  );
}
