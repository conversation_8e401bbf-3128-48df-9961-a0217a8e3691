import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { 
  Check, 
  X, 
  Zap, 
  Crown, 
  Building2,
  ArrowRight,
  Sparkles,
  Star
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

const Pricing = () => {
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: "Free",
      description: "Perfect for trying out BrandForge",
      icon: Zap,
      color: "text-gray-600",
      bgColor: "bg-gray-100",
      price: { monthly: 0, yearly: 0 },
      features: [
        { name: "10 brand name generations per month", included: true },
        { name: "Basic style presets", included: true },
        { name: "Domain availability check", included: "Limited" },
        { name: "Save up to 5 favorites", included: true },
        { name: "Basic export options", included: false },
        { name: "API access", included: false },
        { name: "Priority support", included: false },
        { name: "Bulk generation", included: false }
      ],
      cta: "Get Started Free",
      popular: false
    },
    {
      name: "Pro",
      description: "For serious entrepreneurs and agencies",
      icon: Crown,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      price: { monthly: 29, yearly: 290 },
      features: [
        { name: "Unlimited brand name generations", included: true },
        { name: "All style presets & customization", included: true },
        { name: "Full domain availability check", included: true },
        { name: "Unlimited favorites & collections", included: true },
        { name: "Professional export options", included: true },
        { name: "API access (10,000 requests/month)", included: true },
        { name: "Priority email support", included: true },
        { name: "Bulk generation (up to 1,000)", included: true },
        { name: "Trademark screening", included: true },
        { name: "Analytics dashboard", included: true }
      ],
      cta: "Start Pro Trial",
      popular: true
    },
    {
      name: "Enterprise",
      description: "For large teams and custom needs",
      icon: Building2,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      price: { monthly: 99, yearly: 990 },
      features: [
        { name: "Everything in Pro", included: true },
        { name: "Unlimited API requests", included: true },
        { name: "Team collaboration tools", included: true },
        { name: "Custom integrations", included: true },
        { name: "Dedicated account manager", included: true },
        { name: "Custom style presets", included: true },
        { name: "White-label options", included: true },
        { name: "SLA guarantee", included: true },
        { name: "Advanced analytics", included: true },
        { name: "Priority phone support", included: true }
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  const faqs = [
    {
      question: "Can I change plans anytime?",
      answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and we'll prorate any billing differences."
    },
    {
      question: "What happens to my data if I cancel?",
      answer: "Your saved favorites and generated names remain accessible for 30 days after cancellation. You can export your data anytime during this period."
    },
    {
      question: "Do you offer refunds?",
      answer: "We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact us for a full refund."
    },
    {
      question: "Is there a limit on API usage?",
      answer: "Pro plans include 10,000 API requests per month. Enterprise plans have unlimited usage. Additional requests can be purchased if needed."
    }
  ];

  const getPrice = (plan: typeof plans[0]) => {
    if (plan.price.monthly === 0) return "Free";
    const price = isYearly ? plan.price.yearly : plan.price.monthly;
    const period = isYearly ? "year" : "month";
    return `$${price}/${period}`;
  };

  const getSavings = (plan: typeof plans[0]) => {
    if (plan.price.monthly === 0) return null;
    const yearlyTotal = plan.price.yearly;
    const monthlyTotal = plan.price.monthly * 12;
    const savings = monthlyTotal - yearlyTotal;
    const percentage = Math.round((savings / monthlyTotal) * 100);
    return { amount: savings, percentage };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <Sparkles className="w-4 h-4 mr-2" />
                Simple Pricing
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                Choose Your{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Perfect Plan
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                Start free and scale as you grow. All plans include our core AI-powered 
                brand name generation with no hidden fees.
              </p>

              {/* Billing Toggle */}
              <div className="flex items-center justify-center space-x-4 mb-12">
                <Label htmlFor="billing-toggle" className="text-sm font-medium">
                  Monthly
                </Label>
                <Switch
                  id="billing-toggle"
                  checked={isYearly}
                  onCheckedChange={setIsYearly}
                />
                <Label htmlFor="billing-toggle" className="text-sm font-medium">
                  Yearly
                </Label>
                <Badge variant="secondary" className="ml-2">
                  Save up to 17%
                </Badge>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {plans.map((plan, index) => {
              const savings = getSavings(plan);
              return (
                <motion.div
                  key={plan.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="relative"
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1">
                        <Star className="w-4 h-4 mr-1" />
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <Card className={`h-full ${plan.popular ? 'ring-2 ring-blue-600 shadow-xl' : 'hover:shadow-lg'} transition-all duration-300`}>
                    <CardHeader className="text-center pb-8">
                      <div className={`w-16 h-16 rounded-full ${plan.bgColor} flex items-center justify-center mx-auto mb-4`}>
                        <plan.icon className={`w-8 h-8 ${plan.color}`} />
                      </div>
                      <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                      <CardDescription className="text-base">{plan.description}</CardDescription>
                      
                      <div className="mt-6">
                        <div className="text-4xl font-bold text-gray-900 dark:text-white">
                          {getPrice(plan)}
                        </div>
                        {savings && isYearly && (
                          <div className="text-sm text-green-600 font-medium mt-1">
                            Save ${savings.amount} ({savings.percentage}% off)
                          </div>
                        )}
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <ul className="space-y-3">
                        {plan.features.map((feature, i) => (
                          <li key={i} className="flex items-start">
                            {feature.included === true ? (
                              <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            ) : feature.included === false ? (
                              <X className="w-5 h-5 text-gray-300 mr-3 mt-0.5 flex-shrink-0" />
                            ) : (
                              <Check className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
                            )}
                            <span className={`text-sm ${feature.included === false ? 'text-gray-400' : 'text-gray-700 dark:text-gray-300'}`}>
                              {feature.name}
                              {typeof feature.included === 'string' && (
                                <span className="text-yellow-600 ml-1">({feature.included})</span>
                              )}
                            </span>
                          </li>
                        ))}
                      </ul>
                      
                      <div className="pt-6">
                        <Button 
                          className={`w-full ${plan.popular ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700' : ''}`}
                          variant={plan.popular ? "default" : "outline"}
                        >
                          {plan.cta}
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Frequently Asked Questions
            </h2>
          </motion.div>

          <div className="max-w-3xl mx-auto space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={faq.question}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300">{faq.answer}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Pricing;
