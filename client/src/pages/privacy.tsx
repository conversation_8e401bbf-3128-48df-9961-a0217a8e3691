import { motion } from "framer-motion";
import { 
  Shield, 
  Eye, 
  Lock,
  Database,
  Globe,
  Mail,
  Calendar,
  FileText
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

const Privacy = () => {
  const lastUpdated = "January 20, 2024";

  const sections = [
    {
      id: "information-collection",
      title: "Information We Collect",
      icon: Database,
      content: [
        {
          subtitle: "Personal Information",
          text: "When you create an account, we collect your name, email address, and payment information. This information is necessary to provide our services and process transactions."
        },
        {
          subtitle: "Usage Data",
          text: "We automatically collect information about how you use BrandForge, including the brand names you generate, your preferences, and interaction patterns. This helps us improve our AI algorithms and user experience."
        },
        {
          subtitle: "Technical Information",
          text: "We collect technical data such as your IP address, browser type, device information, and operating system. This information is used for security, analytics, and service optimization."
        }
      ]
    },
    {
      id: "information-use",
      title: "How We Use Your Information",
      icon: Eye,
      content: [
        {
          subtitle: "Service Provision",
          text: "We use your information to provide, maintain, and improve BrandForge services, including AI-powered brand name generation and related features."
        },
        {
          subtitle: "Communication",
          text: "We may use your contact information to send you service updates, security alerts, and promotional materials (which you can opt out of at any time)."
        },
        {
          subtitle: "Analytics and Improvement",
          text: "We analyze usage patterns to improve our AI algorithms, develop new features, and enhance user experience."
        }
      ]
    },
    {
      id: "information-sharing",
      title: "Information Sharing",
      icon: Globe,
      content: [
        {
          subtitle: "Third-Party Services",
          text: "We use trusted third-party services for payment processing (Stripe), analytics (Google Analytics), and infrastructure (AWS). These partners have access only to information necessary for their services."
        },
        {
          subtitle: "Legal Requirements",
          text: "We may disclose your information if required by law, court order, or to protect our rights, property, or safety, or that of our users or the public."
        },
        {
          subtitle: "Business Transfers",
          text: "In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the business transaction."
        }
      ]
    },
    {
      id: "data-security",
      title: "Data Security",
      icon: Lock,
      content: [
        {
          subtitle: "Encryption",
          text: "All data transmission is encrypted using industry-standard SSL/TLS protocols. Sensitive data is encrypted at rest using AES-256 encryption."
        },
        {
          subtitle: "Access Controls",
          text: "We implement strict access controls and authentication mechanisms to ensure only authorized personnel can access user data."
        },
        {
          subtitle: "Regular Audits",
          text: "We conduct regular security audits and vulnerability assessments to maintain the highest security standards."
        }
      ]
    },
    {
      id: "cookies",
      title: "Cookies and Tracking",
      icon: FileText,
      content: [
        {
          subtitle: "Essential Cookies",
          text: "We use essential cookies to maintain your session, remember your preferences, and ensure proper functionality of our services."
        },
        {
          subtitle: "Analytics Cookies",
          text: "We use Google Analytics and similar services to understand how users interact with our platform. You can opt out of analytics tracking in your browser settings."
        },
        {
          subtitle: "Advertising",
          text: "We may use Google AdSense and other advertising networks that use cookies to show relevant ads. You can control ad personalization through your Google account settings."
        }
      ]
    },
    {
      id: "user-rights",
      title: "Your Rights",
      icon: Shield,
      content: [
        {
          subtitle: "Access and Portability",
          text: "You have the right to access your personal data and request a copy in a portable format."
        },
        {
          subtitle: "Correction and Deletion",
          text: "You can update your personal information through your account settings or request deletion of your account and associated data."
        },
        {
          subtitle: "Opt-Out",
          text: "You can opt out of marketing communications and certain data processing activities through your account settings or by contacting us."
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <Shield className="w-4 h-4 mr-2" />
                Privacy Policy
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                Your Privacy{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Matters
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                We're committed to protecting your privacy and being transparent about how we 
                collect, use, and protect your personal information.
              </p>

              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Calendar className="w-4 h-4" />
                <span>Last updated: {lastUpdated}</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Quick Summary */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Alert className="mb-12">
                <Shield className="h-4 w-4" />
                <AlertDescription className="text-base">
                  <strong>Quick Summary:</strong> We collect minimal personal information necessary to provide our services, 
                  use industry-standard security measures to protect your data, and never sell your personal information 
                  to third parties. You have full control over your data and can delete your account at any time.
                </AlertDescription>
              </Alert>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Privacy Sections */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto space-y-12">
            {sections.map((section, index) => (
              <motion.div
                key={section.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                id={section.id}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-2xl">
                      <section.icon className="w-6 h-6 mr-3 text-blue-600" />
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {section.content.map((item, itemIndex) => (
                      <div key={itemIndex}>
                        <h4 className="font-semibold text-lg mb-2 text-gray-900 dark:text-white">
                          {item.subtitle}
                        </h4>
                        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                          {item.text}
                        </p>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Data Retention */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-2xl">
                    <Database className="w-6 h-6 mr-3 text-blue-600" />
                    Data Retention
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    We retain your personal information only as long as necessary to provide our services and comply with legal obligations:
                  </p>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span><strong>Account Data:</strong> Retained while your account is active and for 30 days after deletion</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span><strong>Usage Data:</strong> Aggregated and anonymized data may be retained for analytics purposes</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span><strong>Payment Data:</strong> Retained as required by financial regulations (typically 7 years)</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* International Transfers */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-2xl">
                    <Globe className="w-6 h-6 mr-3 text-blue-600" />
                    International Data Transfers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                    BrandForge operates globally and may transfer your personal information to countries outside your residence. 
                    We ensure appropriate safeguards are in place:
                  </p>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span>Standard Contractual Clauses approved by the European Commission</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span>Adequacy decisions for transfers to countries with adequate protection</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span>Certification schemes and codes of conduct where applicable</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-2xl">
                    <Mail className="w-6 h-6 mr-3 text-blue-600" />
                    Contact Us About Privacy
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                    If you have questions about this Privacy Policy or our data practices, please contact us:
                  </p>
                  <div className="space-y-2 text-gray-600 dark:text-gray-300">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Address:</strong> BrandForge Inc., 123 Innovation Street, San Francisco, CA 94105</p>
                    <p><strong>Data Protection Officer:</strong> <EMAIL></p>
                  </div>
                  <Alert className="mt-6">
                    <Shield className="h-4 w-4" />
                    <AlertDescription>
                      We will respond to privacy-related inquiries within 30 days. For urgent matters, 
                      please mark your email as "Urgent Privacy Request."
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Changes to Policy */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-2xl">
                    <FileText className="w-6 h-6 mr-3 text-blue-600" />
                    Changes to This Policy
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    We may update this Privacy Policy from time to time to reflect changes in our practices, 
                    technology, legal requirements, or other factors. We will notify you of any material changes 
                    by email and by posting the updated policy on our website. Your continued use of BrandForge 
                    after such changes constitutes acceptance of the updated Privacy Policy.
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Privacy;
