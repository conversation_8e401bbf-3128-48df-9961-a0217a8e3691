import OpenAI from "openai";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY || "demo_key"
});

export interface GeneratedBrandName {
  name: string;
  description: string;
  available: 'true' | 'false' | 'premium';
  letterCount: number;
  domainType: 'com' | 'io' | 'co' | 'ai' | 'app';
  domainPrice: string;
  category: string;
  brandability: 'excellent' | 'good' | 'fair';
}

// Determine professional domain recommendations based on keyword and name characteristics
const getDomainRecommendations = (name: string, keyword: string) => {
  const nameLower = name.toLowerCase();
  const keywordLower = keyword.toLowerCase();
  
  // Tech/Software/SaaS categories
  if (keywordLower.includes('tech') || keywordLower.includes('software') || keywordLower.includes('app') || keywordLower.includes('ai')) {
    return {
      domainType: 'io' as const,
      domainPrice: '$89/year',
      category: 'Technology/SaaS',
      brandability: 'excellent' as const
    };
  }
  
  // Business/Finance/Professional services
  if (keywordLower.includes('business') || keywordLower.includes('finance') || keywordLower.includes('consulting')) {
    return {
      domainType: 'com' as const,
      domainPrice: '$12/year',
      category: 'Professional Services',
      brandability: 'excellent' as const
    };
  }
  
  // Creative/Design/Media
  if (keywordLower.includes('design') || keywordLower.includes('creative') || keywordLower.includes('media')) {
    return {
      domainType: 'co' as const,
      domainPrice: '$32/year',
      category: 'Creative Industry',
      brandability: 'good' as const
    };
  }
  
  // Short names (4 letters or less) - premium domains
  if (name.length <= 4) {
    return {
      domainType: 'com' as const,
      domainPrice: '$2,500+/year',
      category: 'Premium Short Domain',
      brandability: 'excellent' as const
    };
  }
  
  // Default for general business
  return {
    domainType: 'com' as const,
    domainPrice: '$12/year',
    category: 'General Business',
    brandability: 'good' as const
  };
};

// Generate brand names based on style and keyword
export const generateNamesByStyle = (keyword: string, style: string): GeneratedBrandName[] => {
  const keywordLower = keyword.toLowerCase();
  
  switch (style) {
    case 'short-catchy':
      return [
        { 
          name: "Zyx", 
          description: "Ultra-short, memorable name perfect for modern brands", 
          available: "true" as const, 
          letterCount: 3,
          ...getDomainRecommendations("Zyx", keyword)
        },
        { 
          name: "Flux", 
          description: "Dynamic 4-letter name suggesting movement and change", 
          available: "false" as const, 
          letterCount: 4,
          ...getDomainRecommendations("Flux", keyword)
        },
        { 
          name: "Vibe", 
          description: "Simple, trendy name that captures energy and feeling", 
          available: "premium" as const, 
          letterCount: 4,
          ...getDomainRecommendations("Vibe", keyword)
        },
        { 
          name: "Peak", 
          description: "Strong, aspirational name suggesting excellence", 
          available: "true" as const, 
          letterCount: 4,
          ...getDomainRecommendations("Peak", keyword)
        },
        { 
          name: "Edge", 
          description: "Sharp, modern name implying cutting-edge innovation", 
          available: "false" as const, 
          letterCount: 4,
          ...getDomainRecommendations("Edge", keyword)
        },
        { 
          name: "Spark", 
          description: "Energetic name suggesting inspiration and creativity", 
          available: "true" as const, 
          letterCount: 5,
          ...getDomainRecommendations("Spark", keyword)
        }
      ];
      
    case 'real-words':
      return [
        { 
          name: "Horizon", 
          description: "Expansive word suggesting limitless possibilities", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Horizon", keyword)
        },
        { 
          name: "Compass", 
          description: "Directional word perfect for guidance and navigation", 
          available: "false" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Compass", keyword)
        },
        { 
          name: "Summit", 
          description: "Achievement-focused word suggesting reaching the top", 
          available: "premium" as const, 
          letterCount: 6,
          ...getDomainRecommendations("Summit", keyword)
        },
        { 
          name: "Pulse", 
          description: "Rhythmic word suggesting life and energy", 
          available: "true" as const, 
          letterCount: 5,
          ...getDomainRecommendations("Pulse", keyword)
        },
        { 
          name: "Grove", 
          description: "Natural word suggesting growth and community", 
          available: "false" as const, 
          letterCount: 5,
          ...getDomainRecommendations("Grove", keyword)
        },
        { 
          name: "Quest", 
          description: "Adventure word suggesting journey and discovery", 
          available: "true" as const, 
          letterCount: 5,
          ...getDomainRecommendations("Quest", keyword)
        }
      ];
      
    case 'compound':
      return [
        { 
          name: "PowerCore", 
          description: "Strong compound suggesting central strength", 
          available: "true" as const, 
          letterCount: 9,
          ...getDomainRecommendations("PowerCore", keyword)
        },
        { 
          name: "FlexFlow", 
          description: "Dynamic compound combining flexibility and movement", 
          available: "false" as const, 
          letterCount: 8,
          ...getDomainRecommendations("FlexFlow", keyword)
        },
        { 
          name: "SmartEdge", 
          description: "Tech compound suggesting intelligent advantage", 
          available: "premium" as const, 
          letterCount: 9,
          ...getDomainRecommendations("SmartEdge", keyword)
        },
        { 
          name: "NextWave", 
          description: "Forward-looking compound suggesting innovation", 
          available: "true" as const, 
          letterCount: 8,
          ...getDomainRecommendations("NextWave", keyword)
        },
        { 
          name: "FreshStart", 
          description: "Positive compound suggesting new beginnings", 
          available: "false" as const, 
          letterCount: 10,
          ...getDomainRecommendations("FreshStart", keyword)
        },
        { 
          name: "TrueForm", 
          description: "Authentic compound suggesting genuine quality", 
          available: "true" as const, 
          letterCount: 8,
          ...getDomainRecommendations("TrueForm", keyword)
        }
      ];
      
    case 'invented':
      return [
        { 
          name: "Zephyra", 
          description: "Invented name with ethereal, premium feel", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Zephyra", keyword)
        },
        { 
          name: "Nexova", 
          description: "Tech-inspired invented name suggesting innovation", 
          available: "false" as const, 
          letterCount: 6,
          ...getDomainRecommendations("Nexova", keyword)
        },
        { 
          name: "Lumira", 
          description: "Light-inspired invented name with elegant sound", 
          available: "premium" as const, 
          letterCount: 6,
          ...getDomainRecommendations("Lumira", keyword)
        },
        { 
          name: "Vortex", 
          description: "Power-inspired invented name suggesting energy", 
          available: "true" as const, 
          letterCount: 6,
          ...getDomainRecommendations("Vortex", keyword)
        },
        { 
          name: "Zenova", 
          description: "Zen-inspired invented name with modern twist", 
          available: "false" as const, 
          letterCount: 6,
          ...getDomainRecommendations("Zenova", keyword)
        },
        { 
          name: "Axira", 
          description: "Strong invented name with memorable pronunciation", 
          available: "true" as const, 
          letterCount: 5,
          ...getDomainRecommendations("Axira", keyword)
        }
      ];
      
    case 'trendy-startup':
      return [
        { 
          name: "Buildly", 
          description: "Trendy -ly suffix suggesting construction and growth", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Buildly", keyword)
        },
        { 
          name: "Streamio", 
          description: "Modern -io suffix perfect for tech platforms", 
          available: "false" as const, 
          letterCount: 8,
          ...getDomainRecommendations("Streamio", keyword)
        },
        { 
          name: "Growify", 
          description: "Action-oriented -ify suffix suggesting transformation", 
          available: "premium" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Growify", keyword)
        },
        { 
          name: "Flexly", 
          description: "Simple -ly suffix emphasizing adaptability", 
          available: "true" as const, 
          letterCount: 6,
          ...getDomainRecommendations("Flexly", keyword)
        },
        { 
          name: "Boostio", 
          description: "Energy-focused -io suffix for dynamic brands", 
          available: "false" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Boostio", keyword)
        },
        { 
          name: "Scalefy", 
          description: "Growth-oriented -fy suffix for scaling businesses", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Scalefy", keyword)
        }
      ];
      
    case 'luxury-premium':
      return [
        { 
          name: "Aurelia", 
          description: "Luxurious name with golden, premium associations", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Aurelia", keyword)
        },
        { 
          name: "Valence", 
          description: "Sophisticated name suggesting value and excellence", 
          available: "false" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Valence", keyword)
        },
        { 
          name: "Meridian", 
          description: "Premium name suggesting pinnacle and precision", 
          available: "premium" as const, 
          letterCount: 8,
          ...getDomainRecommendations("Meridian", keyword)
        },
        { 
          name: "Elysian", 
          description: "Elegant name with heavenly, premium connotations", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Elysian", keyword)
        },
        { 
          name: "Vestige", 
          description: "Refined name suggesting heritage and quality", 
          available: "false" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Vestige", keyword)
        },
        { 
          name: "Opulent", 
          description: "Direct luxury name suggesting wealth and richness", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Opulent", keyword)
        }
      ];
      
    default:
      return [
        { 
          name: "Brandify", 
          description: "Perfect branding-focused name - suggests transformation into a strong brand", 
          available: "true" as const, 
          letterCount: 8,
          ...getDomainRecommendations("Brandify", keyword)
        },
        { 
          name: "Launchpad", 
          description: "Great for startups - suggests the beginning of something big", 
          available: "false" as const, 
          letterCount: 9,
          ...getDomainRecommendations("Launchpad", keyword)
        },
        { 
          name: "Sparkle", 
          description: "Bright, positive name that works across many industries", 
          available: "premium" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Sparkle", keyword)
        },
        { 
          name: "Innovex", 
          description: "Innovation-focused with tech suffix - perfect for forward-thinking companies", 
          available: "true" as const, 
          letterCount: 7,
          ...getDomainRecommendations("Innovex", keyword)
        },
        { 
          name: "Momentum", 
          description: "Suggests progress and movement - ideal for growth-focused businesses", 
          available: "false" as const, 
          letterCount: 8,
          ...getDomainRecommendations("Momentum", keyword)
        },
        { 
          name: "Catalyst", 
          description: "Implies transformation and change - great for consulting or services", 
          available: "true" as const, 
          letterCount: 8,
          ...getDomainRecommendations("Catalyst", keyword)
        }
      ];
  }
};

export async function generateBrandNames(keyword: string): Promise<GeneratedBrandName[]> {
  try {
    const prompt = `Generate 6 unique, catchy, and brandable names for a business in the "${keyword}" niche. 

Requirements:
- Mix of invented names, blended words, and tech-style names
- Names should be 6-12 letters long
- Easy to pronounce and remember
- Suitable for startups and modern businesses
- Include a variety of styles: compound words, made-up words, tech-sounding names

For each name, provide:
- The brand name
- A brief description explaining why it's good for this niche
- Estimated domain availability (true/false/premium - be realistic)
- Letter count

Respond with JSON in this exact format:
{
  "names": [
    {
      "name": "string",
      "description": "string", 
      "available": "true|false|premium",
      "letterCount": number
    }
  ]
}`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert brand naming consultant with deep knowledge of linguistics, marketing, and domain availability patterns."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.8,
      max_tokens: 1000
    });

    const result = JSON.parse(response.choices[0].message.content || "{}");
    
    if (!result.names || !Array.isArray(result.names)) {
      throw new Error("Invalid response format from OpenAI");
    }

    return result.names.map((name: any) => ({
      name: name.name,
      description: name.description,
      available: name.available as 'true' | 'false' | 'premium',
      letterCount: name.letterCount || name.name.length
    }));

  } catch (error: any) {
    // Re-throw the error to be handled by the calling function
    throw new Error("Failed to generate brand names. Please try again.");
  }
}
