import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { generateNamesRequestSchema } from "@shared/schema";
import { generateBrandNames, generateNamesByStyle } from "./services/openai";

export async function registerRoutes(app: Express): Promise<Server> {
  // Generate brand names endpoint
  app.post("/api/generate-names", async (req, res) => {
    try {
      const { keyword, style } = generateNamesRequestSchema.parse(req.body);
      
      // Generate names using style-based generation or OpenAI
      const generatedNames = style 
        ? generateNamesByStyle(keyword, style)
        : await generateBrandNames(keyword);
      
      // Save generated names to storage and preserve domain information
      const savedNames = await Promise.all(
        generatedNames.map((name: any) => 
          storage.saveBrandName({
            name: name.name,
            description: name.description,
            keyword: keyword,
            available: name.available,
            letterCount: name.letterCount
          })
        )
      );

      // Add domain information back to response
      const enhancedNames = savedNames.map((savedName, index) => ({
        ...savedName,
        domainType: generatedNames[index].domainType,
        domainPrice: generatedNames[index].domainPrice,
        category: generatedNames[index].category,
        brandability: generatedNames[index].brandability
      }));

      res.json({ 
        names: savedNames,
        keyword: keyword,
        style: style || 'mixed',
        count: savedNames.length
      });
    } catch (error) {
      console.error("Error generating names:", error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : "Failed to generate brand names" 
      });
    }
  });

  // Get brand names by keyword
  app.get("/api/names/:keyword", async (req, res) => {
    try {
      const keyword = req.params.keyword;
      const names = await storage.getBrandNamesByKeyword(keyword);
      
      res.json({ 
        names,
        keyword,
        count: names.length
      });
    } catch (error) {
      console.error("Error fetching names:", error);
      res.status(500).json({ 
        error: "Failed to fetch brand names" 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
