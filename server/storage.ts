import { users, brandNames, type User, type InsertUser, type BrandN<PERSON>, type InsertBrandName } from "@shared/schema";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  saveBrandName(brandName: InsertBrandName): Promise<BrandName>;
  getBrandNamesByKeyword(keyword: string): Promise<BrandName[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private brandNames: Map<number, BrandName>;
  private currentUserId: number;
  private currentBrandNameId: number;

  constructor() {
    this.users = new Map();
    this.brandNames = new Map();
    this.currentUserId = 1;
    this.currentBrandNameId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async saveBrandName(insertBrandName: InsertBrandName): Promise<BrandName> {
    const id = this.currentBrandNameId++;
    const brandName: BrandName = { 
      ...insertBrandName, 
      id,
      createdAt: new Date()
    };
    this.brandNames.set(id, brandName);
    return brandName;
  }

  async getBrandNamesByKeyword(keyword: string): Promise<BrandName[]> {
    return Array.from(this.brandNames.values()).filter(
      (brandName) => brandName.keyword.toLowerCase() === keyword.toLowerCase()
    );
  }
}

export const storage = new MemStorage();
